{% extends "base.html" %}

{% block title %}العمليات المالية - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-exchange-alt me-2"></i>
        العمليات المالية
    </h1>
    <div class="btn-group" role="group">
        <button class="btn btn-success" onclick="showAddTransactionModal('income')">
            <i class="fas fa-plus me-2"></i>
            إيراد
        </button>
        <button class="btn btn-danger" onclick="showAddTransactionModal('expense')">
            <i class="fas fa-minus me-2"></i>
            مصروف
        </button>
        <button class="btn btn-warning" onclick="showAddTransactionModal('withdrawal')">
            <i class="fas fa-arrow-up me-2"></i>
            سحب
        </button>
        <button class="btn btn-info" onclick="showAddTransactionModal('loan')">
            <i class="fas fa-hand-holding-usd me-2"></i>
            سلفة
        </button>
    </div>
</div>

<!-- اختيار الصندوق والفلاتر -->
<div class="row mb-4">
    <div class="col-md-3">
        <label for="fund_select" class="form-label">اختر الصندوق</label>
        <select class="form-control" id="fund_select" onchange="loadTransactions()">
            <option value="">اختر الصندوق</option>
            {% for fund in funds %}
            <option value="{{ fund.id }}">{{ fund.name }} ({{ fund.fund_type }})</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-3">
        <label for="transaction_type_filter" class="form-label">نوع العملية</label>
        <select class="form-control" id="transaction_type_filter" onchange="filterTransactions()">
            <option value="">جميع العمليات</option>
            <option value="income">إيرادات</option>
            <option value="expense">مصروفات</option>
            <option value="withdrawal">سحوبات</option>
            <option value="loan">سلفيات</option>
            <option value="deposit">إيداعات</option>
        </select>
    </div>
    <div class="col-md-3">
        <label for="date_from" class="form-label">من تاريخ</label>
        <input type="date" class="form-control" id="date_from" onchange="filterTransactions()">
    </div>
    <div class="col-md-3">
        <label for="date_to" class="form-label">إلى تاريخ</label>
        <input type="date" class="form-control" id="date_to" onchange="filterTransactions()">
    </div>
</div>

<!-- ملخص العمليات -->
<div class="row mb-4" id="transactions_summary" style="display: none;">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h6>إجمالي الإيرادات</h6>
                <h4 id="total_income">0.00 ريال</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h6>إجمالي المصروفات</h6>
                <h4 id="total_expense">0.00 ريال</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h6>إجمالي السحوبات</h6>
                <h4 id="total_withdrawal">0.00 ريال</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h6>صافي الرصيد</h6>
                <h4 id="net_balance">0.00 ريال</h4>
            </div>
        </div>
    </div>
</div>

<!-- جدول العمليات -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            سجل العمليات المالية
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped" id="transactions_table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>الوصف</th>
                        <th>العضو/العائلة</th>
                        <th>المستخدم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="transactions_tbody">
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            يرجى اختيار صندوق لعرض العمليات
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- مودال إضافة عملية مالية -->
<div class="modal fade" id="addTransactionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transaction_modal_title">إضافة عملية مالية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTransactionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع العملية</label>
                                <select class="form-control" id="transaction_type" required>
                                    <option value="income">إيراد</option>
                                    <option value="expense">مصروف</option>
                                    <option value="withdrawal">سحب</option>
                                    <option value="loan">سلفة</option>
                                    <option value="deposit">إيداع</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" class="form-control" id="transaction_amount" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="transaction_description" rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="transaction_date" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">التطبيق على</label>
                        <select class="form-control" id="apply_to" onchange="toggleTargetSelection()">
                            <option value="all">جميع الأعضاء</option>
                            <option value="member">عضو محدد</option>
                            <option value="family">عائلة محددة</option>
                            <option value="multiple">أعضاء متعددين</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="target_selection" style="display: none;">
                        <label class="form-label">اختر الهدف</label>
                        <select class="form-control" id="target_select">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </select>
                    </div>
                    
                    <div class="mb-3" id="multiple_selection" style="display: none;">
                        <label class="form-label">اختر الأعضاء</label>
                        <div id="members_checkboxes" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto_distribute">
                            <label class="form-check-label" for="auto_distribute">
                                توزيع المبلغ تلقائياً على الأعضاء المختارين
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addTransaction()">إضافة العملية</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل العملية -->
<div class="modal fade" id="transactionDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transaction_details_content">
                <!-- تفاصيل العملية ستظهر هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allTransactions = [];
let currentFundId = null;

// تحميل العمليات
function loadTransactions() {
    const fundId = document.getElementById('fund_select').value;
    currentFundId = fundId;
    
    if (!fundId) {
        document.getElementById('transactions_tbody').innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    يرجى اختيار صندوق لعرض العمليات
                </td>
            </tr>
        `;
        document.getElementById('transactions_summary').style.display = 'none';
        return;
    }
    
    fetch(`/api/funds/${fundId}/transactions`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            allTransactions = result.data;
            displayTransactions(allTransactions);
            updateSummary(allTransactions);
            loadFundData(fundId);
        } else {
            showAlert('خطأ في تحميل العمليات: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل العمليات', 'danger');
    });
}

// عرض العمليات
function displayTransactions(transactions) {
    const tbody = document.getElementById('transactions_tbody');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    لا توجد عمليات في هذا الصندوق
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    transactions.forEach(transaction => {
        const typeInfo = getTransactionTypeInfo(transaction.transaction_type);
        const amountClass = getAmountClass(transaction.transaction_type);
        
        html += `
            <tr>
                <td>${new Date(transaction.transaction_date).toLocaleDateString('ar-SA')}</td>
                <td>
                    <span class="badge ${typeInfo.class}">
                        <i class="${typeInfo.icon} me-1"></i>
                        ${typeInfo.text}
                    </span>
                </td>
                <td class="${amountClass}">
                    <strong>${transaction.amount.toFixed(2)} ريال</strong>
                </td>
                <td>${transaction.description}</td>
                <td>${transaction.member_name || transaction.family_name || 'جميع الأعضاء'}</td>
                <td>${transaction.created_by_name || '-'}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-primary" onclick="viewTransactionDetails(${transaction.id})" title="تفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editTransaction(${transaction.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTransaction(${transaction.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// تحديث الملخص
function updateSummary(transactions) {
    let totalIncome = 0;
    let totalExpense = 0;
    let totalWithdrawal = 0;
    let totalLoan = 0;
    
    transactions.forEach(transaction => {
        switch(transaction.transaction_type) {
            case 'income':
            case 'deposit':
                totalIncome += transaction.amount;
                break;
            case 'expense':
                totalExpense += transaction.amount;
                break;
            case 'withdrawal':
                totalWithdrawal += transaction.amount;
                break;
            case 'loan':
                totalLoan += transaction.amount;
                break;
        }
    });
    
    const netBalance = totalIncome - totalExpense - totalWithdrawal - totalLoan;
    
    document.getElementById('total_income').textContent = totalIncome.toFixed(2) + ' ريال';
    document.getElementById('total_expense').textContent = totalExpense.toFixed(2) + ' ريال';
    document.getElementById('total_withdrawal').textContent = totalWithdrawal.toFixed(2) + ' ريال';
    document.getElementById('net_balance').textContent = netBalance.toFixed(2) + ' ريال';
    
    document.getElementById('transactions_summary').style.display = 'flex';
}

// تصفية العمليات
function filterTransactions() {
    const typeFilter = document.getElementById('transaction_type_filter').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    
    let filteredTransactions = allTransactions;
    
    // تصفية حسب النوع
    if (typeFilter) {
        filteredTransactions = filteredTransactions.filter(t => t.transaction_type === typeFilter);
    }
    
    // تصفية حسب التاريخ
    if (dateFrom) {
        filteredTransactions = filteredTransactions.filter(t => 
            new Date(t.transaction_date) >= new Date(dateFrom)
        );
    }
    
    if (dateTo) {
        filteredTransactions = filteredTransactions.filter(t => 
            new Date(t.transaction_date) <= new Date(dateTo)
        );
    }
    
    displayTransactions(filteredTransactions);
    updateSummary(filteredTransactions);
}

// الحصول على معلومات نوع العملية
function getTransactionTypeInfo(type) {
    switch(type) {
        case 'income':
            return { text: 'إيراد', class: 'bg-success', icon: 'fas fa-plus' };
        case 'expense':
            return { text: 'مصروف', class: 'bg-danger', icon: 'fas fa-minus' };
        case 'withdrawal':
            return { text: 'سحب', class: 'bg-warning', icon: 'fas fa-arrow-up' };
        case 'loan':
            return { text: 'سلفة', class: 'bg-info', icon: 'fas fa-hand-holding-usd' };
        case 'deposit':
            return { text: 'إيداع', class: 'bg-primary', icon: 'fas fa-arrow-down' };
        default:
            return { text: 'غير محدد', class: 'bg-secondary', icon: 'fas fa-question' };
    }
}

// الحصول على فئة لون المبلغ
function getAmountClass(type) {
    switch(type) {
        case 'income':
        case 'deposit':
            return 'text-success';
        case 'expense':
        case 'withdrawal':
        case 'loan':
            return 'text-danger';
        default:
            return '';
    }
}

// إظهار مودال إضافة عملية
function showAddTransactionModal(type = 'income') {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }
    
    document.getElementById('addTransactionForm').reset();
    document.getElementById('transaction_type').value = type;
    document.getElementById('transaction_date').value = new Date().toISOString().split('T')[0];
    
    const typeInfo = getTransactionTypeInfo(type);
    document.getElementById('transaction_modal_title').textContent = `إضافة ${typeInfo.text}`;
    
    toggleTargetSelection();
    
    const modal = new bootstrap.Modal(document.getElementById('addTransactionModal'));
    modal.show();
}

// تبديل اختيار الهدف
function toggleTargetSelection() {
    const applyTo = document.getElementById('apply_to').value;
    const targetSelection = document.getElementById('target_selection');
    const multipleSelection = document.getElementById('multiple_selection');
    
    targetSelection.style.display = 'none';
    multipleSelection.style.display = 'none';
    
    if (applyTo === 'member' || applyTo === 'family') {
        targetSelection.style.display = 'block';
        loadTargetOptions(applyTo);
    } else if (applyTo === 'multiple') {
        multipleSelection.style.display = 'block';
        loadMembersCheckboxes();
    }
}

// تحميل خيارات الهدف
function loadTargetOptions(type) {
    const targetSelect = document.getElementById('target_select');
    
    if (type === 'member') {
        fetch(`/api/funds/${currentFundId}/members`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                targetSelect.innerHTML = '<option value="">اختر العضو</option>';
                result.data.forEach(member => {
                    targetSelect.innerHTML += `<option value="${member.id}">${member.name}</option>`;
                });
            }
        });
    } else if (type === 'family') {
        fetch(`/api/funds/${currentFundId}/families`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                targetSelect.innerHTML = '<option value="">اختر العائلة</option>';
                result.data.forEach(family => {
                    targetSelect.innerHTML += `<option value="${family.id}">${family.name}</option>`;
                });
            }
        });
    }
}

// تحميل صناديق الاختيار للأعضاء
function loadMembersCheckboxes() {
    fetch(`/api/funds/${currentFundId}/members`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const container = document.getElementById('members_checkboxes');
            let html = '';
            
            result.data.forEach(member => {
                html += `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${member.id}" id="member_${member.id}">
                        <label class="form-check-label" for="member_${member.id}">
                            ${member.name} (${member.balance.toFixed(2)} ريال)
                        </label>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
    });
}

// تحميل بيانات الصندوق
function loadFundData(fundId) {
    // تحميل الأعضاء والعائلات للاستخدام في النماذج
    loadTargetOptions('member');
    loadTargetOptions('family');
}

// إضافة عملية مالية
function addTransaction() {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }

    const type = document.getElementById('transaction_type').value;
    const amount = parseFloat(document.getElementById('transaction_amount').value);
    const description = document.getElementById('transaction_description').value;
    const date = document.getElementById('transaction_date').value;
    const applyTo = document.getElementById('apply_to').value;

    if (!amount || !description || !date) {
        showAlert('يرجى إدخال جميع البيانات المطلوبة', 'warning');
        return;
    }

    let targetId = null;
    let memberIds = [];

    if (applyTo === 'member' || applyTo === 'family') {
        targetId = document.getElementById('target_select').value;
        if (!targetId) {
            showAlert('يرجى اختيار الهدف', 'warning');
            return;
        }
    } else if (applyTo === 'multiple') {
        const checkboxes = document.querySelectorAll('#members_checkboxes input[type="checkbox"]:checked');
        memberIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
        if (memberIds.length === 0) {
            showAlert('يرجى اختيار عضو واحد على الأقل', 'warning');
            return;
        }
    }

    const data = {
        transaction_type: type,
        amount: amount,
        description: description,
        transaction_date: date,
        apply_to: applyTo,
        target_id: targetId,
        member_ids: memberIds,
        auto_distribute: document.getElementById('auto_distribute').checked
    };

    fetch(`/api/funds/${currentFundId}/transactions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('addTransactionModal')).hide();
            showAlert('تم إضافة العملية بنجاح', 'success');
            loadTransactions();
        } else {
            showAlert('خطأ في إضافة العملية: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إضافة العملية', 'danger');
    });
}

// عرض تفاصيل العملية
function viewTransactionDetails(transactionId) {
    showAlert('ميزة عرض التفاصيل قيد التطوير', 'info');
}

// تعديل العملية
function editTransaction(transactionId) {
    showAlert('ميزة التعديل قيد التطوير', 'info');
}

// حذف العملية
function deleteTransaction(transactionId) {
    if (!confirm('هل أنت متأكد من حذف هذه العملية؟')) {
        return;
    }
    
    showAlert('ميزة الحذف قيد التطوير', 'info');
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ الحالي للفلاتر
    const today = new Date().toISOString().split('T')[0];
    const monthAgo = new Date();
    monthAgo.setMonth(monthAgo.getMonth() - 1);
    
    document.getElementById('date_from').value = monthAgo.toISOString().split('T')[0];
    document.getElementById('date_to').value = today;
    
    // تحديد الصندوق من URL إذا كان موجوداً
    const urlParams = new URLSearchParams(window.location.search);
    const fundId = urlParams.get('fund_id');
    if (fundId) {
        document.getElementById('fund_select').value = fundId;
        loadTransactions();
    }
});
</script>
{% endblock %}
