#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت البناء الشامل لتطبيق صندوق التوفير
يقوم ببناء جميع الإصدارات: APK، EXE، Web App
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path
import logging
import argparse
from datetime import datetime

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scripts/build.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TawfeerBuilder:
    """بناء تطبيق صندوق التوفير"""
    
    def __init__(self):
        """تهيئة البناء"""
        self.project_root = Path(__file__).parent.parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        
        # إنشاء المجلدات المطلوبة
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
        # معلومات التطبيق
        self.app_name = "TawfeerApp"
        self.app_version = "1.0.0"
        self.package_name = "com.tawfeer.app"
        
        logger.info("تم تهيئة بناء تطبيق صندوق التوفير")
    
    def install_dependencies(self):
        """تثبيت المتطلبات"""
        logger.info("تثبيت المتطلبات...")
        
        try:
            # تثبيت المتطلبات الأساسية
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True, cwd=self.project_root)
            
            # تثبيت أدوات البناء
            build_requirements = [
                "buildozer",
                "pyinstaller",
                "cx-freeze",
                "auto-py-to-exe"
            ]
            
            for req in build_requirements:
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", req
                    ], check=True)
                    logger.info(f"تم تثبيت {req}")
                except subprocess.CalledProcessError:
                    logger.warning(f"فشل في تثبيت {req}")
            
            logger.info("✅ تم تثبيت المتطلبات بنجاح")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False
    
    def build_android_apk(self):
        """بناء تطبيق Android APK"""
        logger.info("🤖 بدء بناء تطبيق Android...")
        
        try:
            # إنشاء ملف buildozer.spec إذا لم يكن موجوداً
            buildozer_spec = self.project_root / "buildozer.spec"
            if not buildozer_spec.exists():
                self.create_buildozer_spec()
            
            # تشغيل buildozer
            subprocess.run([
                "buildozer", "android", "debug"
            ], check=True, cwd=self.project_root)
            
            # نسخ ملف APK إلى مجلد التوزيع
            apk_source = self.project_root / "bin" / f"{self.app_name}-{self.app_version}-debug.apk"
            apk_dest = self.dist_dir / f"TawfeerApp-{self.app_version}.apk"
            
            if apk_source.exists():
                shutil.copy2(apk_source, apk_dest)
                logger.info(f"✅ تم بناء APK بنجاح: {apk_dest}")
                return True
            else:
                logger.error("❌ لم يتم العثور على ملف APK")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ خطأ في بناء APK: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع في بناء APK: {e}")
            return False
    
    def build_windows_exe(self):
        """بناء تطبيق Windows EXE"""
        logger.info("🪟 بدء بناء تطبيق Windows...")
        
        try:
            # إنشاء ملف spec لـ PyInstaller
            spec_content = self.create_pyinstaller_spec()
            spec_file = self.project_root / "tawfeer.spec"
            
            with open(spec_file, 'w', encoding='utf-8') as f:
                f.write(spec_content)
            
            # تشغيل PyInstaller
            subprocess.run([
                "pyinstaller", "--clean", str(spec_file)
            ], check=True, cwd=self.project_root)
            
            # نسخ الملفات إلى مجلد التوزيع
            exe_source = self.project_root / "dist" / "TawfeerApp"
            exe_dest = self.dist_dir / f"TawfeerApp-Windows-{self.app_version}"
            
            if exe_source.exists():
                if exe_dest.exists():
                    shutil.rmtree(exe_dest)
                shutil.copytree(exe_source, exe_dest)
                
                # إنشاء ملف ZIP
                shutil.make_archive(
                    str(exe_dest),
                    'zip',
                    str(exe_dest)
                )
                
                logger.info(f"✅ تم بناء EXE بنجاح: {exe_dest}.zip")
                return True
            else:
                logger.error("❌ لم يتم العثور على ملفات EXE")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ خطأ في بناء EXE: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع في بناء EXE: {e}")
            return False
    
    def build_web_app(self):
        """إعداد تطبيق الويب للنشر"""
        logger.info("🌐 بدء إعداد تطبيق الويب...")
        
        try:
            # إنشاء مجلد تطبيق الويب
            web_dest = self.dist_dir / f"TawfeerApp-Web-{self.app_version}"
            
            if web_dest.exists():
                shutil.rmtree(web_dest)
            
            web_dest.mkdir(parents=True)
            
            # نسخ الملفات المطلوبة
            files_to_copy = [
                "web_app.py",
                "requirements.txt",
                "src",
                "web",
                "data",
                "config"
            ]
            
            for item in files_to_copy:
                source = self.project_root / item
                if source.exists():
                    if source.is_dir():
                        shutil.copytree(source, web_dest / item)
                    else:
                        shutil.copy2(source, web_dest / item)
            
            # إنشاء ملف تشغيل
            startup_script = web_dest / "start_web.py"
            with open(startup_script, 'w', encoding='utf-8') as f:
                f.write("""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uvicorn
from web_app import app

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False
    )
""")
            
            # إنشاء ملف Docker
            dockerfile = web_dest / "Dockerfile"
            with open(dockerfile, 'w', encoding='utf-8') as f:
                f.write("""FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "start_web.py"]
""")
            
            # إنشاء ملف ZIP
            shutil.make_archive(
                str(web_dest),
                'zip',
                str(web_dest)
            )
            
            logger.info(f"✅ تم إعداد تطبيق الويب بنجاح: {web_dest}.zip")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إعداد تطبيق الويب: {e}")
            return False
    
    def create_buildozer_spec(self):
        """إنشاء ملف buildozer.spec"""
        spec_content = f"""[app]
title = صندوق التوفير
package.name = tawfeerapp
package.domain = com.tawfeer

source.dir = .
source.include_exts = py,png,jpg,kv,atlas,txt,db

version = {self.app_version}
requirements = python3,kivy,kivymd,sqlalchemy,bcrypt,cryptography,pillow

[buildozer]
log_level = 2

[app]
android.permissions = INTERNET,WRITE_EXTERNAL_STORAGE,READ_EXTERNAL_STORAGE

android.api = 30
android.minapi = 21
android.sdk = 30
android.ndk = 23b
android.gradle_dependencies = 

android.add_src = 
android.add_aars = 

android.gradle_repositories = 
android.add_compile_options = 

android.add_gradle_repositories = 
android.add_packaging_options = 

[buildozer]
warn_on_root = 1
"""
        
        with open(self.project_root / "buildozer.spec", 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        logger.info("تم إنشاء ملف buildozer.spec")
    
    def create_pyinstaller_spec(self):
        """إنشاء محتوى ملف PyInstaller spec"""
        return f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('web', 'web'),
        ('data', 'data'),
        ('config', 'config'),
    ],
    hiddenimports=[
        'kivymd',
        'kivymd.uix',
        'kivymd.theming',
        'sqlalchemy',
        'bcrypt',
        'cryptography',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='TawfeerApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/ui/assets/images/icon.ico'
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TawfeerApp'
)
"""
    
    def build_all(self):
        """بناء جميع الإصدارات"""
        logger.info("🚀 بدء بناء جميع إصدارات التطبيق...")
        
        start_time = datetime.now()
        results = {}
        
        # تثبيت المتطلبات
        if not self.install_dependencies():
            logger.error("❌ فشل في تثبيت المتطلبات")
            return False
        
        # بناء تطبيق الويب
        results['web'] = self.build_web_app()
        
        # بناء Windows EXE (فقط على Windows)
        if platform.system() == "Windows":
            results['windows'] = self.build_windows_exe()
        else:
            logger.info("⏭️ تخطي بناء Windows EXE (النظام ليس Windows)")
            results['windows'] = None
        
        # بناء Android APK (يتطلب إعداد خاص)
        try:
            results['android'] = self.build_android_apk()
        except Exception as e:
            logger.warning(f"⚠️ تخطي بناء Android APK: {e}")
            results['android'] = None
        
        # تقرير النتائج
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 50)
        logger.info("📊 تقرير البناء:")
        logger.info(f"⏱️ المدة الإجمالية: {duration}")
        
        for platform_name, success in results.items():
            if success is True:
                logger.info(f"✅ {platform_name}: نجح")
            elif success is False:
                logger.info(f"❌ {platform_name}: فشل")
            else:
                logger.info(f"⏭️ {platform_name}: تم التخطي")
        
        logger.info("=" * 50)
        
        # التحقق من وجود ملفات في مجلد التوزيع
        dist_files = list(self.dist_dir.glob("*"))
        if dist_files:
            logger.info("📦 الملفات المُنشأة:")
            for file in dist_files:
                logger.info(f"   - {file.name}")
        
        return any(results.values())

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="بناء تطبيق صندوق التوفير")
    parser.add_argument(
        "--platform",
        choices=["all", "android", "windows", "web"],
        default="all",
        help="المنصة المراد البناء لها"
    )
    
    args = parser.parse_args()
    
    builder = TawfeerBuilder()
    
    if args.platform == "all":
        success = builder.build_all()
    elif args.platform == "android":
        success = builder.build_android_apk()
    elif args.platform == "windows":
        success = builder.build_windows_exe()
    elif args.platform == "web":
        success = builder.build_web_app()
    
    if success:
        logger.info("🎉 تم البناء بنجاح!")
        return 0
    else:
        logger.error("💥 فشل في البناء!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
