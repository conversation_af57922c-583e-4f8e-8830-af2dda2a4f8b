#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول عبر الويب
"""

import requests
import sys

def test_web_login():
    """اختبار تسجيل الدخول عبر الويب"""
    
    base_url = "http://localhost:8000"
    
    print("🌐 اختبار تسجيل الدخول عبر الويب...")
    
    try:
        # اختبار الصفحة الرئيسية
        print("1. اختبار الصفحة الرئيسية...")
        response = requests.get(f"{base_url}/")
        print(f"   - حالة الاستجابة: {response.status_code}")
        
        # اختبار صفحة تسجيل الدخول
        print("2. اختبار صفحة تسجيل الدخول...")
        response = requests.get(f"{base_url}/login")
        print(f"   - حالة الاستجابة: {response.status_code}")
        
        # اختبار تسجيل الدخول
        print("3. اختبار تسجيل الدخول...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        session = requests.Session()
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        print(f"   - حالة الاستجابة: {response.status_code}")
        print(f"   - الهيدرز: {dict(response.headers)}")
        
        if response.status_code == 302:
            print("✅ تسجيل الدخول نجح! (إعادة توجيه)")
            
            # اختبار الوصول للوحة التحكم
            print("4. اختبار الوصول للوحة التحكم...")
            dashboard_response = session.get(f"{base_url}/dashboard")
            print(f"   - حالة الاستجابة: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                print("✅ الوصول للوحة التحكم نجح!")
            else:
                print("❌ فشل في الوصول للوحة التحكم")
                
        else:
            print("❌ تسجيل الدخول فشل!")
            print(f"   - محتوى الاستجابة: {response.text[:200]}...")
        
        print("\n" + "="*50)
        print("🎯 للدخول للتطبيق:")
        print(f"   الرابط: {base_url}")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("="*50)
        
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم!")
        print("   تأكد من أن الخادم يعمل على http://localhost:8000")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_web_login()
