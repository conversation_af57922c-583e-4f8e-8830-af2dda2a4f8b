{% extends "base.html" %}

{% block title %}لوحة التحكم - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="text-muted">
        مرحباً، {{ user.full_name }}
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ stats.funds_count }}</div>
            <div class="stats-label">
                <i class="fas fa-folder-open me-2"></i>
                الصناديق
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="stats-number">{{ stats.members_count }}</div>
            <div class="stats-label">
                <i class="fas fa-users me-2"></i>
                الأعضاء
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="stats-number">{{ "%.2f"|format(stats.total_balance) }}</div>
            <div class="stats-label">
                <i class="fas fa-coins me-2"></i>
                إجمالي الرصيد
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="stats-number" id="transactions-count">0</div>
            <div class="stats-label">
                <i class="fas fa-exchange-alt me-2"></i>
                العمليات اليوم
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-primary w-100" onclick="showCreateFundModal()">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء صندوق جديد
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-success w-100" onclick="showAddMemberModal()">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عضو
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-warning w-100" onclick="showAddTransactionModal()">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            عملية مالية
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-info w-100" onclick="generateReport()">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير سريع
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الصناديق الحديثة -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    الصناديق الحديثة
                </h5>
                <a href="/funds" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div id="recent-funds">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    التنبيهات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    مرحباً بك في صندوق التوفير!
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تذكير: قم بإنشاء نسخة احتياطية
                </div>
            </div>
        </div>
    </div>
</div>

<!-- العمليات الحديثة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    العمليات الحديثة
                </h5>
                <a href="/transactions" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div id="recent-transactions">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        جاري التحميل...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء صندوق -->
<div class="modal fade" id="createFundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء صندوق جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createFundForm">
                    <div class="mb-3">
                        <label for="fund_name" class="form-label">اسم الصندوق</label>
                        <input type="text" class="form-control" id="fund_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fund_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="fund_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fund_type" class="form-label">نوع الصندوق</label>
                        <select class="form-control" id="fund_type" name="fund_type" required>
                            <option value="family">عائلي</option>
                            <option value="individual">فردي</option>
                            <option value="personal">شخصي</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fund_password" class="form-label">كلمة مرور الصندوق (اختيارية)</label>
                        <input type="password" class="form-control" id="fund_password" name="password">
                        <small class="form-text text-muted">اتركها فارغة إذا لم تكن تريد حماية الصندوق</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createFund()">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء الصندوق
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadRecentFunds();
    loadRecentTransactions();
    updateTransactionsCount();
});

// تحميل الصناديق الحديثة
function loadRecentFunds() {
    fetch('/api/funds')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recent-funds');
            if (data.success && data.data.length > 0) {
                const funds = data.data.slice(0, 5); // أحدث 5 صناديق
                let html = '';
                
                funds.forEach(fund => {
                    html += `
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div>
                                <strong>${fund.name}</strong>
                                <br>
                                <small class="text-muted">${fund.fund_type} - ${fund.members_count} عضو</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">${fund.total_balance.toFixed(2)}</span>
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
            } else {
                container.innerHTML = '<div class="text-center text-muted">لا توجد صناديق</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('recent-funds').innerHTML = 
                '<div class="text-center text-danger">خطأ في تحميل البيانات</div>';
        });
}

// تحميل العمليات الحديثة
function loadRecentTransactions() {
    // TODO: تنفيذ تحميل العمليات الحديثة
    document.getElementById('recent-transactions').innerHTML = 
        '<div class="text-center text-muted">لا توجد عمليات حديثة</div>';
}

// تحديث عدد العمليات اليوم
function updateTransactionsCount() {
    // TODO: تنفيذ حساب عدد العمليات اليوم
    document.getElementById('transactions-count').textContent = '0';
}

// عرض نافذة إنشاء صندوق
function showCreateFundModal() {
    const modal = new bootstrap.Modal(document.getElementById('createFundModal'));
    modal.show();
}

// إنشاء صندوق جديد
function createFund() {
    const form = document.getElementById('createFundForm');
    const formData = new FormData(form);
    
    fetch('/api/funds', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إنشاء الصندوق بنجاح!');
            const modal = bootstrap.Modal.getInstance(document.getElementById('createFundModal'));
            modal.hide();
            form.reset();
            loadRecentFunds();
            location.reload(); // إعادة تحميل الصفحة لتحديث الإحصائيات
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إنشاء الصندوق');
    });
}

// عرض نافذة إضافة عضو
function showAddMemberModal() {
    alert('ميزة إضافة عضو قيد التطوير');
}

// عرض نافذة إضافة عملية مالية
function showAddTransactionModal() {
    alert('ميزة إضافة عملية مالية قيد التطوير');
}

// إنشاء تقرير سريع
function generateReport() {
    alert('ميزة التقارير قيد التطوير');
}
</script>
{% endblock %}
