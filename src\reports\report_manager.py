#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التقارير - إنشاء وتصدير التقارير المالية
يحتوي على جميع الوظائف المتعلقة بإنشاء التقارير وتصديرها
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from datetime import datetime, date, timedelta
import logging
import io
import base64

from ..database.models import Transaction, Fund, Member, Family, TransactionType, User
from ..database.database import db_manager

logger = logging.getLogger(__name__)

class ReportManager:
    """مدير التقارير"""
    
    def __init__(self):
        """تهيئة مدير التقارير"""
        pass
    
    def generate_fund_summary_report(
        self,
        fund_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        إنشاء تقرير ملخص الصندوق
        
        Args:
            fund_id: معرف الصندوق
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            تقرير ملخص الصندوق
        """
        try:
            with db_manager.get_session() as session:
                # الحصول على معلومات الصندوق
                fund = session.query(Fund).filter_by(id=fund_id, is_active=True).first()
                if not fund:
                    return {"success": False, "message": "الصندوق غير موجود"}
                
                # تحديد الفترة الزمنية
                if not start_date:
                    start_date = date.today() - timedelta(days=30)
                if not end_date:
                    end_date = date.today()
                
                # استعلام العمليات في الفترة المحددة
                query = session.query(Transaction).filter(
                    and_(
                        Transaction.fund_id == fund_id,
                        Transaction.transaction_date >= start_date,
                        Transaction.transaction_date <= end_date
                    )
                )
                
                transactions = query.all()
                
                # حساب الإحصائيات
                total_income = sum(t.amount for t in transactions if t.transaction_type == TransactionType.INCOME)
                total_expense = sum(t.amount for t in transactions if t.transaction_type == TransactionType.EXPENSE)
                total_withdrawals = sum(t.amount for t in transactions if t.transaction_type == TransactionType.WITHDRAWAL)
                total_loans = sum(t.amount for t in transactions if t.transaction_type == TransactionType.LOAN)
                total_deposits = sum(t.amount for t in transactions if t.transaction_type == TransactionType.DEPOSIT)
                
                net_balance = total_income + total_deposits - total_expense - total_withdrawals - total_loans
                
                # إحصائيات الأعضاء
                members = session.query(Member).filter(
                    and_(Member.fund_id == fund_id, Member.is_active == True)
                ).all()
                
                members_summary = []
                for member in members:
                    member_transactions = [t for t in transactions if t.member_id == member.id]
                    member_income = sum(t.amount for t in member_transactions if t.transaction_type == TransactionType.INCOME)
                    member_expense = sum(t.amount for t in member_transactions if t.transaction_type == TransactionType.EXPENSE)
                    
                    members_summary.append({
                        "id": member.id,
                        "name": member.name,
                        "current_balance": member.balance,
                        "period_income": float(member_income),
                        "period_expense": float(member_expense),
                        "transactions_count": len(member_transactions)
                    })
                
                # إحصائيات العائلات
                families = session.query(Family).filter_by(fund_id=fund_id).all()
                families_summary = []
                
                for family in families:
                    family_members = [m for m in members if m.family_id == family.id]
                    family_balance = sum(m.balance for m in family_members)
                    family_transactions = [t for t in transactions if any(t.member_id == m.id for m in family_members)]
                    
                    families_summary.append({
                        "id": family.id,
                        "name": family.name,
                        "members_count": len(family_members),
                        "total_balance": float(family_balance),
                        "transactions_count": len(family_transactions)
                    })
                
                # تجميع التقرير
                report = {
                    "success": True,
                    "fund_info": {
                        "id": fund.id,
                        "name": fund.name,
                        "type": fund.fund_type.value,
                        "current_balance": fund.total_balance
                    },
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "days_count": (end_date - start_date).days + 1
                    },
                    "financial_summary": {
                        "total_income": float(total_income),
                        "total_expense": float(total_expense),
                        "total_withdrawals": float(total_withdrawals),
                        "total_loans": float(total_loans),
                        "total_deposits": float(total_deposits),
                        "net_balance": float(net_balance),
                        "transactions_count": len(transactions)
                    },
                    "members_summary": members_summary,
                    "families_summary": families_summary,
                    "generated_at": datetime.now().isoformat()
                }
                
                return report
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الصندوق: {e}")
            return {"success": False, "message": f"خطأ في إنشاء التقرير: {str(e)}"}
    
    def generate_member_report(
        self,
        member_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        إنشاء تقرير عضو
        
        Args:
            member_id: معرف العضو
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            تقرير العضو
        """
        try:
            with db_manager.get_session() as session:
                # الحصول على معلومات العضو
                member = session.query(Member).filter(
                    and_(Member.id == member_id, Member.is_active == True)
                ).first()
                
                if not member:
                    return {"success": False, "message": "العضو غير موجود"}
                
                # تحديد الفترة الزمنية
                if not start_date:
                    start_date = date.today() - timedelta(days=30)
                if not end_date:
                    end_date = date.today()
                
                # الحصول على عمليات العضو
                transactions = session.query(Transaction).filter(
                    and_(
                        Transaction.member_id == member_id,
                        Transaction.transaction_date >= start_date,
                        Transaction.transaction_date <= end_date
                    )
                ).order_by(desc(Transaction.transaction_date)).all()
                
                # تجميع العمليات حسب النوع
                transactions_by_type = {}
                for trans_type in TransactionType:
                    type_transactions = [t for t in transactions if t.transaction_type == trans_type]
                    transactions_by_type[trans_type.value] = {
                        "count": len(type_transactions),
                        "total_amount": float(sum(t.amount for t in type_transactions)),
                        "transactions": [
                            {
                                "id": t.id,
                                "amount": t.amount,
                                "description": t.description,
                                "date": t.transaction_date.isoformat()
                            } for t in type_transactions
                        ]
                    }
                
                # معلومات العائلة
                family_info = None
                if member.family_id:
                    family = session.query(Family).filter_by(id=member.family_id).first()
                    if family:
                        family_info = {
                            "id": family.id,
                            "name": family.name,
                            "total_members": family.total_members
                        }
                
                # تجميع التقرير
                report = {
                    "success": True,
                    "member_info": {
                        "id": member.id,
                        "name": member.name,
                        "phone": member.phone,
                        "email": member.email,
                        "current_balance": member.balance,
                        "family": family_info
                    },
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "days_count": (end_date - start_date).days + 1
                    },
                    "transactions_summary": transactions_by_type,
                    "total_transactions": len(transactions),
                    "generated_at": datetime.now().isoformat()
                }
                
                return report
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير العضو: {e}")
            return {"success": False, "message": f"خطأ في إنشاء التقرير: {str(e)}"}
    
    def generate_daily_report(self, fund_id: int, target_date: Optional[date] = None) -> Dict[str, Any]:
        """إنشاء تقرير يومي"""
        if not target_date:
            target_date = date.today()
        
        return self.generate_fund_summary_report(fund_id, target_date, target_date)
    
    def generate_weekly_report(self, fund_id: int, target_date: Optional[date] = None) -> Dict[str, Any]:
        """إنشاء تقرير أسبوعي"""
        if not target_date:
            target_date = date.today()
        
        start_date = target_date - timedelta(days=target_date.weekday())
        end_date = start_date + timedelta(days=6)
        
        return self.generate_fund_summary_report(fund_id, start_date, end_date)
    
    def generate_monthly_report(self, fund_id: int, year: int = None, month: int = None) -> Dict[str, Any]:
        """إنشاء تقرير شهري"""
        if not year:
            year = date.today().year
        if not month:
            month = date.today().month
        
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        return self.generate_fund_summary_report(fund_id, start_date, end_date)
    
    def generate_yearly_report(self, fund_id: int, year: int = None) -> Dict[str, Any]:
        """إنشاء تقرير سنوي"""
        if not year:
            year = date.today().year
        
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        return self.generate_fund_summary_report(fund_id, start_date, end_date)
    
    def export_report_to_csv(self, report_data: Dict[str, Any]) -> str:
        """تصدير التقرير إلى CSV"""
        try:
            import csv
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # كتابة معلومات التقرير
            writer.writerow(['تقرير الصندوق'])
            writer.writerow(['اسم الصندوق', report_data.get('fund_info', {}).get('name', '')])
            writer.writerow(['فترة التقرير', f"{report_data.get('period', {}).get('start_date', '')} إلى {report_data.get('period', {}).get('end_date', '')}"])
            writer.writerow([])
            
            # كتابة الملخص المالي
            writer.writerow(['الملخص المالي'])
            financial = report_data.get('financial_summary', {})
            writer.writerow(['إجمالي الإيرادات', financial.get('total_income', 0)])
            writer.writerow(['إجمالي المصروفات', financial.get('total_expense', 0)])
            writer.writerow(['إجمالي السحوبات', financial.get('total_withdrawals', 0)])
            writer.writerow(['إجمالي السلفيات', financial.get('total_loans', 0)])
            writer.writerow(['صافي الرصيد', financial.get('net_balance', 0)])
            writer.writerow([])
            
            # كتابة ملخص الأعضاء
            writer.writerow(['ملخص الأعضاء'])
            writer.writerow(['الاسم', 'الرصيد الحالي', 'إيرادات الفترة', 'مصروفات الفترة', 'عدد العمليات'])
            
            for member in report_data.get('members_summary', []):
                writer.writerow([
                    member.get('name', ''),
                    member.get('current_balance', 0),
                    member.get('period_income', 0),
                    member.get('period_expense', 0),
                    member.get('transactions_count', 0)
                ])
            
            csv_content = output.getvalue()
            output.close()
            
            return csv_content
            
        except Exception as e:
            logger.error(f"خطأ في تصدير CSV: {e}")
            return ""
    
    def export_report_to_html(self, report_data: Dict[str, Any]) -> str:
        """تصدير التقرير إلى HTML"""
        try:
            html_template = """
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير الصندوق</title>
                <style>
                    body { font-family: 'Arial', sans-serif; direction: rtl; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .section { margin-bottom: 20px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; }
                    .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير صندوق التوفير</h1>
                    <h2>{fund_name}</h2>
                    <p>من {start_date} إلى {end_date}</p>
                </div>
                
                <div class="section">
                    <h3>الملخص المالي</h3>
                    <div class="summary">
                        <p><strong>إجمالي الإيرادات:</strong> {total_income}</p>
                        <p><strong>إجمالي المصروفات:</strong> {total_expense}</p>
                        <p><strong>إجمالي السحوبات:</strong> {total_withdrawals}</p>
                        <p><strong>إجمالي السلفيات:</strong> {total_loans}</p>
                        <p><strong>صافي الرصيد:</strong> {net_balance}</p>
                        <p><strong>عدد العمليات:</strong> {transactions_count}</p>
                    </div>
                </div>
                
                <div class="section">
                    <h3>ملخص الأعضاء</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الرصيد الحالي</th>
                                <th>إيرادات الفترة</th>
                                <th>مصروفات الفترة</th>
                                <th>عدد العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {members_rows}
                        </tbody>
                    </table>
                </div>
                
                <div class="footer">
                    <p><small>تم إنشاء التقرير في: {generated_at}</small></p>
                </div>
            </body>
            </html>
            """
            
            # تجهيز بيانات الأعضاء
            members_rows = ""
            for member in report_data.get('members_summary', []):
                members_rows += f"""
                <tr>
                    <td>{member.get('name', '')}</td>
                    <td>{member.get('current_balance', 0)}</td>
                    <td>{member.get('period_income', 0)}</td>
                    <td>{member.get('period_expense', 0)}</td>
                    <td>{member.get('transactions_count', 0)}</td>
                </tr>
                """
            
            # ملء القالب
            fund_info = report_data.get('fund_info', {})
            period = report_data.get('period', {})
            financial = report_data.get('financial_summary', {})
            
            html_content = html_template.format(
                fund_name=fund_info.get('name', ''),
                start_date=period.get('start_date', ''),
                end_date=period.get('end_date', ''),
                total_income=financial.get('total_income', 0),
                total_expense=financial.get('total_expense', 0),
                total_withdrawals=financial.get('total_withdrawals', 0),
                total_loans=financial.get('total_loans', 0),
                net_balance=financial.get('net_balance', 0),
                transactions_count=financial.get('transactions_count', 0),
                members_rows=members_rows,
                generated_at=report_data.get('generated_at', '')
            )
            
            return html_content
            
        except Exception as e:
            logger.error(f"خطأ في تصدير HTML: {e}")
            return ""

# إنشاء مثيل مدير التقارير
report_manager = ReportManager()
