# 🏦 صندوق التوفير - Tawfeer Savings Fund

<div align="center">

![صندوق التوفير](https://img.shields.io/badge/صندوق_التوفير-v1.0.0-blue.svg)
![Python](https://img.shields.io/badge/Python-3.10+-green.svg)
![Kivy](https://img.shields.io/badge/Kivy-2.2.0-orange.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-red.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**تطبيق شامل لإدارة الصناديق والأعضاء والعمليات المالية**

[العربية](#العربية) | [English](#english)

</div>

---

## 📋 المحتويات

- [نظرة عامة](#نظرة-عامة)
- [المميزات](#المميزات)
- [متطلبات النظام](#متطلبات-النظام)
- [التثبيت والتشغيل](#التثبيت-والتشغيل)
- [طرق التشغيل](#طرق-التشغيل)
- [البناء والتصدير](#البناء-والتصدير)
- [هيكل المشروع](#هيكل-المشروع)
- [الاستخدام](#الاستخدام)
- [التطوير](#التطوير)
- [المساهمة](#المساهمة)
- [الترخيص](#الترخيص)

---

## 🎯 نظرة عامة

**صندوق التوفير** هو تطبيق احترافي شامل لإدارة الصناديق المالية والأعضاء والعمليات المالية. يدعم التطبيق عدة منصات ويوفر واجهة مستخدم عربية متجاوبة مع دعم كامل لاتجاه النص من اليمين إلى اليسار (RTL).

### 🌟 المنصات المدعومة

- 📱 **Android** - تطبيق APK
- 🖥️ **Windows** - تطبيق EXE
- 🌐 **Web** - تطبيق ويب متجاوب
- 🐧 **Linux** - تطبيق سطح المكتب
- 🍎 **macOS** - تطبيق سطح المكتب

---

## ✨ المميزات

### 🏦 إدارة الصناديق
- ✅ إنشاء صناديق متعددة (عائلي، فردي، شخصي)
- ✅ حماية الصناديق بكلمة مرور
- ✅ إحصائيات شاملة لكل صندوق
- ✅ نسخ احتياطية تلقائية

### 👥 إدارة الأعضاء
- ✅ إضافة/تعديل/حذف الأعضاء
- ✅ استيراد من ملفات CSV/TXT
- ✅ تتبع رصيد كل عضو
- ✅ سجل العمليات لكل عضو

### 👨‍👩‍👧‍👦 إدارة العائلات
- ✅ تجميع الأعضاء في عائلات
- ✅ إحصائيات العائلة
- ✅ توزيع العمليات على العائلة

### 💰 العمليات المالية
- ✅ إيرادات ومصروفات
- ✅ سحوبات وسلفيات
- ✅ توزيع تلقائي على الأعضاء
- ✅ تعديل وحذف العمليات

### 📊 التقارير
- ✅ تقارير يومية/أسبوعية/شهرية/سنوية
- ✅ تصدير PDF/Excel
- ✅ إرسال عبر واتساب/SMS/إيميل
- ✅ تقارير مخصصة

### 🛠️ الخدمات الإضافية
- ✅ تحويل التاريخ (هجري ⇄ ميلادي)
- ✅ حساب المدة بين التواريخ
- ✅ نظام ملاحظات مع تنبيهات
- ✅ تخصيص الألوان

### 🔐 الأمان والصلاحيات
- ✅ نظام مستخدمين متعدد
- ✅ صلاحيات متدرجة (مدير/مشرف/عضو)
- ✅ تشفير البيانات الحساسة
- ✅ مصادقة JWT

---

## 💻 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10, macOS 10.14, Ubuntu 18.04, Android 7.0
- **المعالج**: Intel/AMD x64 أو ARM64
- **الذاكرة**: 2 GB RAM
- **التخزين**: 500 MB مساحة فارغة
- **الشبكة**: اتصال إنترنت (للميزات السحابية)

### للتطوير
- **Python**: 3.10 أو أحدث
- **pip**: أحدث إصدار
- **Git**: للتحكم في الإصدارات

---

## 🚀 التثبيت والتشغيل

### التثبيت السريع

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/tawfeer-app.git
cd tawfeer-app

# 2. تثبيت المتطلبات
pip install -r requirements.txt

# 3. تشغيل التطبيق
python main.py
```

### التثبيت المفصل

#### 1. تحضير البيئة

```bash
# إنشاء بيئة افتراضية (مستحسن)
python -m venv tawfeer_env

# تفعيل البيئة الافتراضية
# على Windows:
tawfeer_env\Scripts\activate
# على Linux/macOS:
source tawfeer_env/bin/activate
```

#### 2. تثبيت المتطلبات

```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المتطلبات
pip install -r requirements.txt
```

#### 3. إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات (تلقائي عند أول تشغيل)
python -c "from src.database.database import db_manager; print('تم إعداد قاعدة البيانات')"
```

---

## 🎮 طرق التشغيل

### 1. تطبيق سطح المكتب (Kivy)

```bash
python main.py
```

### 2. تطبيق الويب (FastAPI)

```bash
python web_app.py
```

ثم افتح المتصفح على: http://localhost:8000

### 3. استخدام السكربت السريع

```bash
# تشغيل تطبيق سطح المكتب
./scripts/quick_build.sh run

# تشغيل خادم الويب
./scripts/quick_build.sh web
```

---

## 📦 البناء والتصدير

### البناء التلقائي لجميع المنصات

```bash
python scripts/build_all.py
```

### البناء لمنصة محددة

```bash
# بناء تطبيق Windows
python scripts/build_all.py --platform windows

# بناء تطبيق Android
python scripts/build_all.py --platform android

# إعداد تطبيق الويب
python scripts/build_all.py --platform web
```

### استخدام السكربت السريع

```bash
# بناء جميع الإصدارات
./scripts/quick_build.sh build-all

# بناء إصدار محدد
./scripts/quick_build.sh build-win
./scripts/quick_build.sh build-android
./scripts/quick_build.sh build-web
```

### متطلبات البناء الإضافية

#### لبناء Android APK:
```bash
# تثبيت buildozer
pip install buildozer

# تثبيت Java JDK 8
# تثبيت Android SDK
# تثبيت Android NDK
```

#### لبناء Windows EXE:
```bash
# تثبيت PyInstaller
pip install pyinstaller
```

---

## 📁 هيكل المشروع

```
tawfeer-app/
├── 📄 main.py                 # التطبيق الرئيسي (Kivy)
├── 📄 web_app.py             # تطبيق الويب (FastAPI)
├── 📄 requirements.txt       # متطلبات Python
├── 📄 README.md             # هذا الملف
├── 📁 src/                  # الكود المصدري
│   ├── 📁 auth/            # نظام المصادقة
│   ├── 📁 core/            # المنطق الأساسي
│   ├── 📁 database/        # قاعدة البيانات
│   ├── 📁 models/          # نماذج البيانات
│   ├── 📁 ui/              # واجهة المستخدم
│   ├── 📁 services/        # الخدمات
│   ├── 📁 utils/           # أدوات مساعدة
│   └── 📁 reports/         # التقارير
├── 📁 web/                 # ملفات الويب
│   ├── 📁 static/          # الملفات الثابتة
│   └── 📁 templates/       # قوالب HTML
├── 📁 scripts/             # سكربتات البناء
├── 📁 data/                # البيانات والنسخ الاحتياطية
├── 📁 config/              # ملفات الإعداد
├── 📁 tests/               # الاختبارات
└── 📁 docs/                # التوثيق
```

---

## 📖 الاستخدام

### تسجيل الدخول الأولي

**المدير الافتراضي:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### الخطوات الأساسية

1. **إنشاء صندوق جديد**
   - اذهب إلى "الصناديق" → "إنشاء صندوق جديد"
   - أدخل اسم الصندوق ونوعه
   - اختر كلمة مرور (اختيارية)

2. **إضافة أعضاء**
   - اختر الصندوق → "الأعضاء" → "إضافة عضو"
   - أدخل بيانات العضو والرصيد الأولي

3. **إنشاء عائلات**
   - اذهب إلى "العائلات" → "إنشاء عائلة"
   - أضف الأعضاء للعائلة

4. **إضافة عمليات مالية**
   - اذهب إلى "العمليات المالية" → "إضافة عملية"
   - اختر نوع العملية والمبلغ والأعضاء

5. **إنشاء التقارير**
   - اذهب إلى "التقارير"
   - اختر نوع التقرير والفترة الزمنية
   - صدّر أو أرسل التقرير

---

## 🛠️ التطوير

### إعداد بيئة التطوير

```bash
# استنساخ المشروع
git clone https://github.com/your-username/tawfeer-app.git
cd tawfeer-app

# إنشاء بيئة افتراضية
python -m venv dev_env
source dev_env/bin/activate  # Linux/macOS
# أو
dev_env\Scripts\activate     # Windows

# تثبيت متطلبات التطوير
pip install -r requirements.txt
pip install pytest black flake8 mypy
```

### تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
pytest

# تشغيل اختبارات محددة
pytest tests/test_auth.py

# تشغيل مع تغطية الكود
pytest --cov=src tests/
```

### فحص جودة الكود

```bash
# تنسيق الكود
black src/ tests/

# فحص الأخطاء
flake8 src/ tests/

# فحص الأنواع
mypy src/
```

### إضافة ميزات جديدة

1. أنشئ فرع جديد: `git checkout -b feature/new-feature`
2. اكتب الكود والاختبارات
3. تأكد من نجاح جميع الاختبارات
4. أرسل طلب دمج (Pull Request)

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. **Fork** المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. تنفيذ التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. رفع التغييرات (`git push origin feature/AmazingFeature`)
5. إنشاء Pull Request

### إرشادات المساهمة

- اكتب كود نظيف ومفهوم
- أضف اختبارات للميزات الجديدة
- حدّث التوثيق عند الحاجة
- اتبع معايير Python PEP 8

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل

- 📧 البريد الإلكتروني: <EMAIL>
- 🐛 الإبلاغ عن الأخطاء: [GitHub Issues](https://github.com/your-username/tawfeer-app/issues)
- 💬 المناقشات: [GitHub Discussions](https://github.com/your-username/tawfeer-app/discussions)

---

## 🙏 شكر وتقدير

- فريق [Kivy](https://kivy.org/) لإطار العمل الرائع
- فريق [FastAPI](https://fastapi.tiangolo.com/) للـ API السريع
- مجتمع Python العربي للدعم والمساعدة

---

<div align="center">

**صُنع بـ ❤️ للمجتمع العربي**

⭐ إذا أعجبك المشروع، لا تنس إعطاؤه نجمة!

</div>
