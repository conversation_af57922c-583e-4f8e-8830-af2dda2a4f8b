#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الويب لتطبيق صندوق التوفير
تطبيق ويب متجاوب باستخدام FastAPI
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, Request, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
# from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
import uvicorn
import logging

# استيراد مدراء النظام
from src.auth.auth_manager import auth_manager
from src.core.fund_manager import fund_manager
from src.core.member_manager import member_manager
from src.core.family_manager import family_manager
from src.core.transaction_manager import transaction_manager
from src.database.database import get_database
from src.database.models import User

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="صندوق التوفير - واجهة الويب",
    description="تطبيق ويب شامل لإدارة الصناديق والأعضاء والعمليات المالية",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الملفات الثابتة والقوالب
app.mount("/static", StaticFiles(directory="web/static"), name="static")
templates = Jinja2Templates(directory="web/templates")

# إعداد المصادقة - تم إزالة HTTPBearer لاستخدام الكوكيز

async def get_current_user(request: Request) -> Optional[User]:
    """الحصول على المستخدم الحالي من الكوكيز"""
    token = request.cookies.get("access_token")
    if not token:
        return None

    user = auth_manager.verify_token_and_get_user(token)
    return user

# الصفحات الرئيسية
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """الصفحة الرئيسية"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """صفحة تسجيل الدخول"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...)
):
    """تسجيل دخول المستخدم"""
    result = auth_manager.login(username, password)
    
    if result["success"]:
        # إعادة توجيه للوحة التحكم
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(
            key="access_token",
            value=result["access_token"],
            httponly=True,
            secure=False  # تعيين True في الإنتاج مع HTTPS
        )
        return response
    else:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": result["message"]}
        )

@app.get("/logout")
async def logout():
    """تسجيل خروج المستخدم"""
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie("access_token")
    return response

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, current_user: User = Depends(get_current_user)):
    """لوحة التحكم"""
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)
    
    # الحصول على إحصائيات المستخدم
    funds = fund_manager.get_user_funds(current_user.id)
    
    total_members = 0
    total_balance = 0
    for fund in funds:
        members = member_manager.get_fund_members(fund["id"])
        total_members += len(members)
        total_balance += fund["total_balance"]
    
    stats = {
        "funds_count": len(funds),
        "members_count": total_members,
        "total_balance": total_balance,
        "user_name": current_user.full_name
    }
    
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "stats": stats, "user": current_user}
    )

# API endpoints
@app.get("/api/funds")
async def get_funds(current_user: User = Depends(get_current_user)):
    """الحصول على صناديق المستخدم"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    funds = fund_manager.get_user_funds(current_user.id)
    return {"success": True, "data": funds}

@app.post("/api/funds")
async def create_fund(
    name: str = Form(...),
    description: str = Form(""),
    fund_type: str = Form(...),
    password: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """إنشاء صندوق جديد"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = fund_manager.create_fund(
        name=name,
        description=description,
        fund_type=fund_type,
        owner_id=current_user.id,
        password=password
    )
    
    return result

@app.get("/api/funds/{fund_id}/members")
async def get_fund_members(fund_id: int, current_user: User = Depends(get_current_user)):
    """الحصول على أعضاء الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    members = member_manager.get_fund_members(fund_id)
    return {"success": True, "data": members}

@app.post("/api/funds/{fund_id}/members")
async def add_member(
    fund_id: int,
    name: str = Form(...),
    phone: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    initial_balance: float = Form(0.0),
    current_user: User = Depends(get_current_user)
):
    """إضافة عضو جديد"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = member_manager.add_member(
        fund_id=fund_id,
        name=name,
        phone=phone,
        email=email,
        initial_balance=initial_balance
    )
    
    return result

@app.get("/api/funds/{fund_id}/families")
async def get_fund_families(fund_id: int, current_user: User = Depends(get_current_user)):
    """الحصول على عائلات الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    families = family_manager.get_fund_families(fund_id)
    return {"success": True, "data": families}

@app.post("/api/funds/{fund_id}/families")
async def create_family(
    fund_id: int,
    name: str = Form(...),
    description: Optional[str] = Form(""),
    current_user: User = Depends(get_current_user)
):
    """إنشاء عائلة جديدة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = family_manager.create_family(
        fund_id=fund_id,
        name=name,
        description=description
    )
    
    return result

@app.get("/api/funds/{fund_id}/transactions")
async def get_fund_transactions(
    fund_id: int,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """الحصول على عمليات الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    transactions = transaction_manager.get_fund_transactions(
        fund_id=fund_id,
        limit=limit,
        offset=offset
    )
    
    return {"success": True, "data": transactions}

@app.post("/api/funds/{fund_id}/transactions")
async def add_transaction(
    fund_id: int,
    amount: float = Form(...),
    transaction_type: str = Form(...),
    description: str = Form(...),
    member_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """إضافة عملية مالية جديدة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = transaction_manager.add_transaction(
        fund_id=fund_id,
        amount=amount,
        transaction_type=transaction_type,
        description=description,
        created_by=current_user.id,
        member_id=member_id
    )
    
    return result

# معالج الأخطاء
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """معالج خطأ 404"""
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error_code": 404, "error_message": "الصفحة غير موجودة"},
        status_code=404
    )

@app.exception_handler(500)
async def server_error_handler(request: Request, exc: HTTPException):
    """معالج خطأ 500"""
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error_code": 500, "error_message": "خطأ في الخادم"},
        status_code=500
    )

if __name__ == "__main__":
    # إنشاء المجلدات المطلوبة
    os.makedirs("logs", exist_ok=True)
    os.makedirs("web/static/css", exist_ok=True)
    os.makedirs("web/static/js", exist_ok=True)
    os.makedirs("web/static/images", exist_ok=True)
    os.makedirs("web/templates", exist_ok=True)
    
    logger.info("بدء تشغيل خادم الويب...")
    
    uvicorn.run(
        "web_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
