#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الويب لتطبيق صندوق التوفير
تطبيق ويب متجاوب باستخدام FastAPI
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, Request, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
# from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
import uvicorn
import logging

# استيراد مدراء النظام
from src.auth.auth_manager import auth_manager
from src.core.fund_manager import fund_manager
from src.core.member_manager import member_manager
from src.core.family_manager import family_manager
from src.core.transaction_manager import transaction_manager
from src.reports.report_manager import report_manager
from src.services.calendar_service import calendar_service
from src.services.notification_service import notification_service
from src.database.database import get_database
from src.database.models import User

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="صندوق التوفير - واجهة الويب",
    description="تطبيق ويب شامل لإدارة الصناديق والأعضاء والعمليات المالية",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الملفات الثابتة والقوالب
app.mount("/static", StaticFiles(directory="web/static"), name="static")
templates = Jinja2Templates(directory="web/templates")

# إعداد المصادقة - تم إزالة HTTPBearer لاستخدام الكوكيز

async def get_current_user(request: Request) -> Optional[User]:
    """الحصول على المستخدم الحالي من الكوكيز"""
    token = request.cookies.get("access_token")
    if not token:
        return None

    user = auth_manager.verify_token_and_get_user(token)
    return user

# الصفحات الرئيسية
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """الصفحة الرئيسية"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """صفحة تسجيل الدخول"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...)
):
    """تسجيل دخول المستخدم"""
    result = auth_manager.login(username, password)
    
    if result["success"]:
        # إعادة توجيه للوحة التحكم
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(
            key="access_token",
            value=result["access_token"],
            httponly=True,
            secure=False  # تعيين True في الإنتاج مع HTTPS
        )
        return response
    else:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": result["message"]}
        )

@app.get("/logout")
async def logout():
    """تسجيل خروج المستخدم"""
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie("access_token")
    return response

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, current_user: User = Depends(get_current_user)):
    """لوحة التحكم"""
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)
    
    # الحصول على إحصائيات المستخدم
    funds = fund_manager.get_user_funds(current_user.id)
    
    total_members = 0
    total_balance = 0
    for fund in funds:
        members = member_manager.get_fund_members(fund["id"])
        total_members += len(members)
        total_balance += fund["total_balance"]
    
    stats = {
        "funds_count": len(funds),
        "members_count": total_members,
        "total_balance": total_balance,
        "user_name": current_user.full_name
    }
    
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "stats": stats, "user": current_user}
    )

# API endpoints
@app.get("/api/funds")
async def get_funds(current_user: User = Depends(get_current_user)):
    """الحصول على صناديق المستخدم"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    funds = fund_manager.get_user_funds(current_user.id)
    return {"success": True, "data": funds}

@app.post("/api/funds")
async def create_fund(
    name: str = Form(...),
    description: str = Form(""),
    fund_type: str = Form(...),
    password: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """إنشاء صندوق جديد"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = fund_manager.create_fund(
        name=name,
        description=description,
        fund_type=fund_type,
        owner_id=current_user.id,
        password=password
    )
    
    return result

@app.get("/api/funds/{fund_id}/members")
async def get_fund_members(fund_id: int, current_user: User = Depends(get_current_user)):
    """الحصول على أعضاء الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    members = member_manager.get_fund_members(fund_id)
    return {"success": True, "data": members}

@app.post("/api/funds/{fund_id}/members")
async def add_member(
    fund_id: int,
    name: str = Form(...),
    phone: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    initial_balance: float = Form(0.0),
    current_user: User = Depends(get_current_user)
):
    """إضافة عضو جديد"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = member_manager.add_member(
        fund_id=fund_id,
        name=name,
        phone=phone,
        email=email,
        initial_balance=initial_balance
    )
    
    return result

@app.get("/api/funds/{fund_id}/families")
async def get_fund_families(fund_id: int, current_user: User = Depends(get_current_user)):
    """الحصول على عائلات الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    families = family_manager.get_fund_families(fund_id)
    return {"success": True, "data": families}

@app.post("/api/funds/{fund_id}/families")
async def create_family(
    fund_id: int,
    name: str = Form(...),
    description: Optional[str] = Form(""),
    current_user: User = Depends(get_current_user)
):
    """إنشاء عائلة جديدة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = family_manager.create_family(
        fund_id=fund_id,
        name=name,
        description=description
    )
    
    return result

@app.get("/api/funds/{fund_id}/transactions")
async def get_fund_transactions(
    fund_id: int,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """الحصول على عمليات الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    transactions = transaction_manager.get_fund_transactions(
        fund_id=fund_id,
        limit=limit,
        offset=offset
    )
    
    return {"success": True, "data": transactions}

@app.post("/api/funds/{fund_id}/transactions")
async def add_transaction(
    fund_id: int,
    amount: float = Form(...),
    transaction_type: str = Form(...),
    description: str = Form(...),
    member_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_user)
):
    """إضافة عملية مالية جديدة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")
    
    result = transaction_manager.add_transaction(
        fund_id=fund_id,
        amount=amount,
        transaction_type=transaction_type,
        description=description,
        created_by=current_user.id,
        member_id=member_id
    )
    
    return result

# التقارير
@app.get("/reports", response_class=HTMLResponse)
async def reports_page(request: Request, current_user: User = Depends(get_current_user)):
    """صفحة التقارير"""
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)

    # الحصول على صناديق المستخدم
    funds = fund_manager.get_user_funds(current_user.id)

    return templates.TemplateResponse(
        "reports.html",
        {"request": request, "funds": funds, "user": current_user}
    )

@app.get("/api/reports/fund/{fund_id}")
async def generate_fund_report(
    fund_id: int,
    report_type: str = "monthly",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """إنشاء تقرير الصندوق"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        from datetime import datetime

        if report_type == "daily":
            target_date = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            report = report_manager.generate_daily_report(fund_id, target_date)
        elif report_type == "weekly":
            target_date = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            report = report_manager.generate_weekly_report(fund_id, target_date)
        elif report_type == "monthly":
            if start_date:
                date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                report = report_manager.generate_monthly_report(fund_id, date_obj.year, date_obj.month)
            else:
                report = report_manager.generate_monthly_report(fund_id)
        elif report_type == "yearly":
            year = int(start_date.split("-")[0]) if start_date else None
            report = report_manager.generate_yearly_report(fund_id, year)
        elif report_type == "custom":
            start = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            end = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
            report = report_manager.generate_fund_summary_report(fund_id, start, end)
        else:
            raise HTTPException(status_code=400, detail="نوع التقرير غير صحيح")

        return report

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء التقرير: {str(e)}")

@app.get("/api/reports/member/{member_id}")
async def generate_member_report(
    member_id: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """إنشاء تقرير العضو"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        from datetime import datetime

        start = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
        end = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None

        report = report_manager.generate_member_report(member_id, start, end)
        return report

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء التقرير: {str(e)}")

@app.get("/api/reports/export/{fund_id}")
async def export_report(
    fund_id: int,
    format: str = "html",
    report_type: str = "monthly",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """تصدير التقرير"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        from fastapi.responses import Response
        from datetime import datetime

        # إنشاء التقرير
        if report_type == "daily":
            target_date = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            report = report_manager.generate_daily_report(fund_id, target_date)
        elif report_type == "monthly":
            if start_date:
                date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                report = report_manager.generate_monthly_report(fund_id, date_obj.year, date_obj.month)
            else:
                report = report_manager.generate_monthly_report(fund_id)
        else:
            start = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            end = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
            report = report_manager.generate_fund_summary_report(fund_id, start, end)

        if not report["success"]:
            raise HTTPException(status_code=400, detail=report["message"])

        # تصدير حسب التنسيق المطلوب
        if format == "csv":
            content = report_manager.export_report_to_csv(report)
            return Response(
                content=content,
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename=report_{fund_id}.csv"}
            )
        elif format == "html":
            content = report_manager.export_report_to_html(report)
            return Response(
                content=content,
                media_type="text/html",
                headers={"Content-Disposition": f"attachment; filename=report_{fund_id}.html"}
            )
        else:
            raise HTTPException(status_code=400, detail="تنسيق التصدير غير مدعوم")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تصدير التقرير: {str(e)}")

# الخدمات الإضافية
@app.get("/services", response_class=HTMLResponse)
async def services_page(request: Request, current_user: User = Depends(get_current_user)):
    """صفحة الخدمات الإضافية"""
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)

    return templates.TemplateResponse(
        "services.html",
        {"request": request, "user": current_user}
    )

# خدمة التقويم
@app.post("/api/services/calendar/convert")
async def convert_calendar(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """تحويل التقويم"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        data = await request.json()
        conversion_type = data.get("type")  # "hijri_to_gregorian" or "gregorian_to_hijri"

        if conversion_type == "hijri_to_gregorian":
            year = int(data.get("year"))
            month = int(data.get("month"))
            day = int(data.get("day"))
            result = calendar_service.hijri_to_gregorian(year, month, day)
        elif conversion_type == "gregorian_to_hijri":
            from datetime import datetime
            date_str = data.get("date")
            date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
            result = calendar_service.gregorian_to_hijri(date_obj)
        else:
            raise HTTPException(status_code=400, detail="نوع التحويل غير صحيح")

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في التحويل: {str(e)}")

@app.post("/api/services/calendar/difference")
async def calculate_date_difference(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """حساب الفرق بين تاريخين"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        from datetime import datetime
        data = await request.json()

        start_date_str = data.get("start_date")
        end_date_str = data.get("end_date")

        start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
        end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()

        result = calendar_service.calculate_date_difference(start_date, end_date)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في الحساب: {str(e)}")

@app.get("/api/services/calendar/current")
async def get_current_date_info(current_user: User = Depends(get_current_user)):
    """الحصول على معلومات التاريخ الحالي"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    return calendar_service.get_current_date_info()

# خدمة الإشعارات
@app.get("/api/notifications")
async def get_notifications(
    unread_only: bool = False,
    current_user: User = Depends(get_current_user)
):
    """الحصول على إشعارات المستخدم"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    notifications = notification_service.get_user_notifications(
        user_id=current_user.id,
        unread_only=unread_only
    )

    return {"success": True, "data": notifications}

@app.post("/api/notifications/{notification_id}/read")
async def mark_notification_read(
    notification_id: int,
    current_user: User = Depends(get_current_user)
):
    """تمييز الإشعار كمقروء"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    result = notification_service.mark_notification_as_read(
        notification_id=notification_id,
        user_id=current_user.id
    )

    return result

# خدمة الملاحظات
@app.get("/api/notes")
async def get_notes(
    fund_id: Optional[int] = None,
    archived: bool = False,
    current_user: User = Depends(get_current_user)
):
    """الحصول على ملاحظات المستخدم"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    notes = notification_service.get_user_notes(
        user_id=current_user.id,
        fund_id=fund_id,
        archived=archived
    )

    return {"success": True, "data": notes}

@app.post("/api/notes")
async def create_note(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """إنشاء ملاحظة جديدة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        data = await request.json()

        title = data.get("title")
        content = data.get("content")
        color = data.get("color", "#ffffff")
        fund_id = data.get("fund_id")
        member_id = data.get("member_id")
        is_pinned = data.get("is_pinned", False)

        # تحويل تاريخ التذكير إذا كان موجوداً
        reminder_at = None
        if data.get("reminder_at"):
            from datetime import datetime
            reminder_at = datetime.fromisoformat(data.get("reminder_at"))

        result = notification_service.create_note(
            title=title,
            content=content,
            user_id=current_user.id,
            color=color,
            fund_id=fund_id,
            member_id=member_id,
            reminder_at=reminder_at,
            is_pinned=is_pinned
        )

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء الملاحظة: {str(e)}")

@app.put("/api/notes/{note_id}")
async def update_note(
    note_id: int,
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """تحديث ملاحظة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    try:
        data = await request.json()

        # تحويل تاريخ التذكير إذا كان موجوداً
        reminder_at = None
        if data.get("reminder_at"):
            from datetime import datetime
            reminder_at = datetime.fromisoformat(data.get("reminder_at"))

        result = notification_service.update_note(
            note_id=note_id,
            user_id=current_user.id,
            title=data.get("title"),
            content=data.get("content"),
            color=data.get("color"),
            is_pinned=data.get("is_pinned"),
            reminder_at=reminder_at
        )

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحديث الملاحظة: {str(e)}")

@app.delete("/api/notes/{note_id}")
async def delete_note(
    note_id: int,
    current_user: User = Depends(get_current_user)
):
    """حذف ملاحظة"""
    if not current_user:
        raise HTTPException(status_code=401, detail="غير مصرح")

    result = notification_service.delete_note(
        note_id=note_id,
        user_id=current_user.id
    )

    return result

# معالج الأخطاء
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """معالج خطأ 404"""
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error_code": 404, "error_message": "الصفحة غير موجودة"},
        status_code=404
    )

@app.exception_handler(500)
async def server_error_handler(request: Request, exc: HTTPException):
    """معالج خطأ 500"""
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error_code": 500, "error_message": "خطأ في الخادم"},
        status_code=500
    )

if __name__ == "__main__":
    # إنشاء المجلدات المطلوبة
    os.makedirs("logs", exist_ok=True)
    os.makedirs("web/static/css", exist_ok=True)
    os.makedirs("web/static/js", exist_ok=True)
    os.makedirs("web/static/images", exist_ok=True)
    os.makedirs("web/templates", exist_ok=True)
    
    logger.info("بدء تشغيل خادم الويب...")
    
    uvicorn.run(
        "web_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
