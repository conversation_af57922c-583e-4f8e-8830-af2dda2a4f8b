#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت التشغيل السريع لتطبيق صندوق التوفير
يوفر خيارات متعددة لتشغيل التطبيق بطرق مختلفة
"""

import os
import sys
import argparse
import subprocess
import webbrowser
from pathlib import Path
import time
import threading

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    🏦 صندوق التوفير 🏦                      ║
    ║                                                              ║
    ║              تطبيق شامل لإدارة الصناديق والأعضاء              ║
    ║                                                              ║
    ║                        الإصدار 1.0.0                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """التحقق من وجود المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    try:
        import kivy
        print("✅ Kivy متوفر")
    except ImportError:
        print("❌ Kivy غير متوفر - يرجى تثبيته: pip install kivy")
        return False
    
    try:
        import kivymd
        print("✅ KivyMD متوفر")
    except ImportError:
        print("❌ KivyMD غير متوفر - يرجى تثبيته: pip install kivymd")
        return False
    
    try:
        import fastapi
        print("✅ FastAPI متوفر")
    except ImportError:
        print("❌ FastAPI غير متوفر - يرجى تثبيته: pip install fastapi")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy متوفر")
    except ImportError:
        print("❌ SQLAlchemy غير متوفر - يرجى تثبيته: pip install sqlalchemy")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        from src.database.database import db_manager
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        
        # عرض معلومات قاعدة البيانات
        info = db_manager.get_database_info()
        print(f"📊 إجمالي السجلات: {info.get('total_records', 0)}")
        
        return True
    except Exception as e:
        print(f"❌ فشل في إعداد قاعدة البيانات: {e}")
        return False

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    print("🖥️ تشغيل تطبيق سطح المكتب...")
    
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تشغيل التطبيق: {e}")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق")

def run_web_app():
    """تشغيل تطبيق الويب"""
    print("🌐 تشغيل تطبيق الويب...")
    print("📍 الرابط: http://localhost:8000")
    
    # فتح المتصفح بعد ثانيتين
    def open_browser():
        time.sleep(2)
        webbrowser.open("http://localhost:8000")
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        subprocess.run([sys.executable, "web_app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تشغيل خادم الويب: {e}")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف خادم الويب")

def run_tests():
    """تشغيل الاختبارات"""
    print("🧪 تشغيل الاختبارات...")
    
    try:
        subprocess.run([sys.executable, "-m", "pytest", "tests/", "-v"], check=True)
        print("✅ جميع الاختبارات نجحت")
    except subprocess.CalledProcessError as e:
        print(f"❌ بعض الاختبارات فشلت: {e}")
    except FileNotFoundError:
        print("❌ pytest غير متوفر - يرجى تثبيته: pip install pytest")

def build_project():
    """بناء المشروع"""
    print("🔨 بناء المشروع...")
    
    try:
        subprocess.run([sys.executable, "scripts/build_all.py"], check=True)
        print("✅ تم بناء المشروع بنجاح")
        print("📦 الملفات متوفرة في مجلد dist/")
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء المشروع: {e}")

def clean_project():
    """تنظيف ملفات المشروع"""
    print("🧹 تنظيف ملفات المشروع...")
    
    import shutil
    
    # المجلدات المراد حذفها
    dirs_to_clean = [
        "build",
        "dist",
        "__pycache__",
        ".pytest_cache",
        "*.egg-info"
    ]
    
    # الملفات المراد حذفها
    files_to_clean = [
        "*.pyc",
        "*.pyo",
        ".coverage"
    ]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ تم حذف {dir_name}")
    
    # حذف ملفات __pycache__ في جميع المجلدات
    for root, dirs, files in os.walk("."):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                shutil.rmtree(os.path.join(root, dir_name))
                print(f"🗑️ تم حذف {os.path.join(root, dir_name)}")
    
    print("✅ تم تنظيف المشروع")

def show_info():
    """عرض معلومات المشروع"""
    print("ℹ️ معلومات المشروع:")
    print("=" * 50)
    print("📛 الاسم: صندوق التوفير")
    print("🔢 الإصدار: 1.0.0")
    print("👨‍💻 المطور: فريق التطوير")
    print("🐍 Python: " + sys.version.split()[0])
    print("📁 المسار: " + str(project_root))
    print("=" * 50)
    
    # معلومات قاعدة البيانات
    try:
        from src.database.database import db_manager
        info = db_manager.get_database_info()
        print("🗄️ معلومات قاعدة البيانات:")
        print(f"   📍 المسار: {info.get('database_path', 'غير محدد')}")
        print(f"   📊 إجمالي السجلات: {info.get('total_records', 0)}")
        print(f"   👥 المستخدمون: {info.get('tables', {}).get('users', 0)}")
        print(f"   🏦 الصناديق: {info.get('tables', {}).get('funds', 0)}")
        print(f"   👨‍👩‍👧‍👦 الأعضاء: {info.get('tables', {}).get('members', 0)}")
    except Exception as e:
        print(f"❌ خطأ في الحصول على معلومات قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="سكربت التشغيل السريع لتطبيق صندوق التوفير",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python run.py                    # عرض القائمة التفاعلية
  python run.py --desktop          # تشغيل تطبيق سطح المكتب
  python run.py --web              # تشغيل تطبيق الويب
  python run.py --install          # تثبيت المتطلبات
  python run.py --test             # تشغيل الاختبارات
  python run.py --build            # بناء المشروع
  python run.py --clean            # تنظيف المشروع
  python run.py --info             # عرض معلومات المشروع
        """
    )
    
    parser.add_argument("--desktop", action="store_true", help="تشغيل تطبيق سطح المكتب")
    parser.add_argument("--web", action="store_true", help="تشغيل تطبيق الويب")
    parser.add_argument("--install", action="store_true", help="تثبيت المتطلبات")
    parser.add_argument("--test", action="store_true", help="تشغيل الاختبارات")
    parser.add_argument("--build", action="store_true", help="بناء المشروع")
    parser.add_argument("--clean", action="store_true", help="تنظيف المشروع")
    parser.add_argument("--info", action="store_true", help="عرض معلومات المشروع")
    parser.add_argument("--setup", action="store_true", help="إعداد المشروع (تثبيت + إعداد قاعدة البيانات)")
    
    args = parser.parse_args()
    
    print_banner()
    
    # تنفيذ الأوامر
    if args.install:
        install_requirements()
    elif args.desktop:
        if check_requirements() and setup_database():
            run_desktop_app()
    elif args.web:
        if check_requirements() and setup_database():
            run_web_app()
    elif args.test:
        if check_requirements():
            run_tests()
    elif args.build:
        if check_requirements():
            build_project()
    elif args.clean:
        clean_project()
    elif args.info:
        show_info()
    elif args.setup:
        if install_requirements():
            setup_database()
    else:
        # القائمة التفاعلية
        show_interactive_menu()

def show_interactive_menu():
    """عرض القائمة التفاعلية"""
    while True:
        print("\n" + "=" * 50)
        print("🎯 اختر العملية المطلوبة:")
        print("=" * 50)
        print("1. 🖥️  تشغيل تطبيق سطح المكتب")
        print("2. 🌐 تشغيل تطبيق الويب")
        print("3. 📦 تثبيت المتطلبات")
        print("4. 🧪 تشغيل الاختبارات")
        print("5. 🔨 بناء المشروع")
        print("6. 🧹 تنظيف المشروع")
        print("7. ℹ️  عرض معلومات المشروع")
        print("8. ⚙️  إعداد المشروع")
        print("0. 🚪 خروج")
        print("=" * 50)
        
        try:
            choice = input("👆 اختر رقم العملية: ").strip()
            
            if choice == "1":
                if check_requirements() and setup_database():
                    run_desktop_app()
            elif choice == "2":
                if check_requirements() and setup_database():
                    run_web_app()
            elif choice == "3":
                install_requirements()
            elif choice == "4":
                if check_requirements():
                    run_tests()
            elif choice == "5":
                if check_requirements():
                    build_project()
            elif choice == "6":
                clean_project()
            elif choice == "7":
                show_info()
            elif choice == "8":
                if install_requirements():
                    setup_database()
            elif choice == "0":
                print("👋 شكراً لاستخدام صندوق التوفير!")
                break
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n👋 شكراً لاستخدام صندوق التوفير!")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
