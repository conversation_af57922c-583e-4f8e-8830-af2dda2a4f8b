#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الأمان والتشفير لتطبيق صندوق التوفير
تحتوي على دوال التشفير والمصادقة وإدارة كلمات المرور
"""

import bcrypt
import jwt
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
import base64
import os

# مفتاح سري للتوقيع (يجب تغييره في الإنتاج)
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

class SecurityManager:
    """مدير الأمان والتشفير"""
    
    def __init__(self):
        """تهيئة مدير الأمان"""
        self.fernet_key = self._get_or_create_fernet_key()
        self.fernet = Fernet(self.fernet_key)
    
    def _get_or_create_fernet_key(self) -> bytes:
        """الحصول على مفتاح التشفير أو إنشاؤه"""
        key_file = "config/encryption.key"
        
        # إنشاء مجلد config إذا لم يكن موجوداً
        os.makedirs("config", exist_ok=True)
        
        if os.path.exists(key_file):
            with open(key_file, "rb") as f:
                return f.read()
        else:
            # إنشاء مفتاح جديد
            key = Fernet.generate_key()
            with open(key_file, "wb") as f:
                f.write(key)
            return key
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        try:
            encrypted_data = self.fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            raise ValueError(f"خطأ في تشفير البيانات: {e}")
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.fernet.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            raise ValueError(f"خطأ في فك تشفير البيانات: {e}")

# إنشاء مثيل مدير الأمان
security_manager = SecurityManager()

def hash_password(password: str) -> str:
    """
    تشفير كلمة المرور باستخدام bcrypt
    
    Args:
        password: كلمة المرور الخام
        
    Returns:
        كلمة المرور المشفرة
    """
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(password: str, hashed_password: str) -> bool:
    """
    التحقق من كلمة المرور
    
    Args:
        password: كلمة المرور الخام
        hashed_password: كلمة المرور المشفرة
        
    Returns:
        True إذا كانت كلمة المرور صحيحة
    """
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception:
        return False

def generate_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    إنشاء رمز JWT
    
    Args:
        data: البيانات المراد تشفيرها في الرمز
        expires_delta: مدة انتهاء صلاحية الرمز
        
    Returns:
        رمز JWT
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    التحقق من رمز JWT
    
    Args:
        token: رمز JWT
        
    Returns:
        البيانات المشفرة في الرمز أو None إذا كان الرمز غير صالح
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.PyJWTError:
        return None

def generate_secure_filename(original_filename: str) -> str:
    """
    إنشاء اسم ملف آمن
    
    Args:
        original_filename: اسم الملف الأصلي
        
    Returns:
        اسم ملف آمن
    """
    # الحصول على امتداد الملف
    file_extension = os.path.splitext(original_filename)[1]
    
    # إنشاء اسم عشوائي آمن
    secure_name = secrets.token_urlsafe(16)
    
    return f"{secure_name}{file_extension}"

def generate_api_key() -> str:
    """إنشاء مفتاح API آمن"""
    return secrets.token_urlsafe(32)

def hash_file(file_path: str) -> str:
    """
    حساب hash للملف للتحقق من سلامته
    
    Args:
        file_path: مسار الملف
        
    Returns:
        hash الملف
    """
    hash_sha256 = hashlib.sha256()
    
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        raise ValueError(f"خطأ في حساب hash الملف: {e}")

def validate_password_strength(password: str) -> Dict[str, Any]:
    """
    التحقق من قوة كلمة المرور
    
    Args:
        password: كلمة المرور
        
    Returns:
        معلومات عن قوة كلمة المرور
    """
    result = {
        "is_valid": True,
        "score": 0,
        "errors": [],
        "suggestions": []
    }
    
    # الحد الأدنى للطول
    if len(password) < 8:
        result["is_valid"] = False
        result["errors"].append("كلمة المرور يجب أن تكون 8 أحرف على الأقل")
    else:
        result["score"] += 1
    
    # وجود أحرف كبيرة
    if not any(c.isupper() for c in password):
        result["suggestions"].append("أضف أحرف كبيرة")
    else:
        result["score"] += 1
    
    # وجود أحرف صغيرة
    if not any(c.islower() for c in password):
        result["suggestions"].append("أضف أحرف صغيرة")
    else:
        result["score"] += 1
    
    # وجود أرقام
    if not any(c.isdigit() for c in password):
        result["suggestions"].append("أضف أرقام")
    else:
        result["score"] += 1
    
    # وجود رموز خاصة
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        result["suggestions"].append("أضف رموز خاصة")
    else:
        result["score"] += 1
    
    # تقييم القوة
    if result["score"] >= 4:
        result["strength"] = "قوية"
    elif result["score"] >= 3:
        result["strength"] = "متوسطة"
    else:
        result["strength"] = "ضعيفة"
    
    return result

def sanitize_input(input_string: str) -> str:
    """
    تنظيف المدخلات من الأحرف الضارة
    
    Args:
        input_string: النص المدخل
        
    Returns:
        النص المنظف
    """
    if not input_string:
        return ""
    
    # إزالة الأحرف الضارة
    dangerous_chars = ["<", ">", "&", "\"", "'", "/", "\\"]
    cleaned = input_string
    
    for char in dangerous_chars:
        cleaned = cleaned.replace(char, "")
    
    # إزالة المسافات الزائدة
    cleaned = cleaned.strip()
    
    return cleaned

def encrypt_sensitive_data(data: str) -> str:
    """تشفير البيانات الحساسة"""
    return security_manager.encrypt_data(data)

def decrypt_sensitive_data(encrypted_data: str) -> str:
    """فك تشفير البيانات الحساسة"""
    return security_manager.decrypt_data(encrypted_data)
