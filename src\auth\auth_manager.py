#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المصادقة والصلاحيات لتطبيق صندوق التوفير
يحتوي على جميع وظائف تسجيل الدخول والخروج وإدارة الصلاحيات
"""

from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime, timedelta
import logging

from ..database.models import User, UserRole
from ..database.database import db_manager
from ..utils.security import (
    hash_password, verify_password, generate_token, 
    verify_token, validate_password_strength
)

logger = logging.getLogger(__name__)

class AuthManager:
    """مدير المصادقة والصلاحيات"""
    
    def __init__(self):
        """تهيئة مدير المصادقة"""
        self.current_user = None
        self.current_token = None
    
    def register_user(
        self,
        username: str,
        email: str,
        password: str,
        full_name: str,
        phone: Optional[str] = None,
        role: str = "member"
    ) -> Dict[str, Any]:
        """
        تسجيل مستخدم جديد
        
        Args:
            username: اسم المستخدم
            email: البريد الإلكتروني
            password: كلمة المرور
            full_name: الاسم الكامل
            phone: رقم الهاتف
            role: دور المستخدم (admin, supervisor, member)
            
        Returns:
            نتيجة التسجيل
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من عدم وجود المستخدم
                existing_user = session.query(User).filter(
                    or_(User.username == username, User.email == email)
                ).first()
                
                if existing_user:
                    if existing_user.username == username:
                        return {"success": False, "message": "اسم المستخدم موجود بالفعل"}
                    else:
                        return {"success": False, "message": "البريد الإلكتروني موجود بالفعل"}
                
                # التحقق من قوة كلمة المرور
                password_check = validate_password_strength(password)
                if not password_check["is_valid"]:
                    return {
                        "success": False, 
                        "message": "كلمة المرور ضعيفة",
                        "errors": password_check["errors"]
                    }
                
                # التحقق من صحة الدور
                try:
                    user_role = UserRole(role)
                except ValueError:
                    return {"success": False, "message": "دور المستخدم غير صحيح"}
                
                # إنشاء المستخدم الجديد
                new_user = User(
                    username=username,
                    email=email,
                    password_hash=hash_password(password),
                    full_name=full_name,
                    phone=phone,
                    role=user_role
                )
                
                session.add(new_user)
                session.commit()
                session.refresh(new_user)
                
                logger.info(f"تم تسجيل مستخدم جديد: {username}")
                
                return {
                    "success": True,
                    "message": "تم تسجيل المستخدم بنجاح",
                    "user": {
                        "id": new_user.id,
                        "username": new_user.username,
                        "email": new_user.email,
                        "full_name": new_user.full_name,
                        "role": new_user.role.value
                    }
                }
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل المستخدم: {e}")
            return {"success": False, "message": f"خطأ في تسجيل المستخدم: {str(e)}"}
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """
        تسجيل دخول المستخدم
        
        Args:
            username: اسم المستخدم أو البريد الإلكتروني
            password: كلمة المرور
            
        Returns:
            نتيجة تسجيل الدخول مع الرمز المميز
        """
        try:
            with db_manager.get_session() as session:
                # البحث عن المستخدم
                user = session.query(User).filter(
                    and_(
                        or_(User.username == username, User.email == username),
                        User.is_active == True
                    )
                ).first()
                
                if not user:
                    return {"success": False, "message": "اسم المستخدم أو كلمة المرور غير صحيحة"}
                
                # التحقق من كلمة المرور
                if not verify_password(password, user.password_hash):
                    return {"success": False, "message": "اسم المستخدم أو كلمة المرور غير صحيحة"}
                
                # إنشاء رمز الوصول
                token_data = {
                    "user_id": user.id,
                    "username": user.username,
                    "role": user.role.value
                }
                
                access_token = generate_token(token_data)
                
                # حفظ المستخدم الحالي
                self.current_user = user
                self.current_token = access_token
                
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                
                return {
                    "success": True,
                    "message": "تم تسجيل الدخول بنجاح",
                    "access_token": access_token,
                    "token_type": "bearer",
                    "user": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "full_name": user.full_name,
                        "phone": user.phone,
                        "role": user.role.value,
                        "created_at": user.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            return {"success": False, "message": f"خطأ في تسجيل الدخول: {str(e)}"}
    
    def logout(self) -> Dict[str, Any]:
        """
        تسجيل خروج المستخدم
        
        Returns:
            نتيجة تسجيل الخروج
        """
        try:
            if self.current_user:
                logger.info(f"تم تسجيل خروج المستخدم: {self.current_user.username}")
            
            self.current_user = None
            self.current_token = None
            
            return {"success": True, "message": "تم تسجيل الخروج بنجاح"}
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الخروج: {e}")
            return {"success": False, "message": f"خطأ في تسجيل الخروج: {str(e)}"}
    
    def verify_token_and_get_user(self, token: str) -> Optional[User]:
        """
        التحقق من الرمز المميز والحصول على المستخدم
        
        Args:
            token: الرمز المميز
            
        Returns:
            المستخدم أو None
        """
        try:
            payload = verify_token(token)
            if not payload:
                return None
            
            user_id = payload.get("user_id")
            if not user_id:
                return None
            
            with db_manager.get_session() as session:
                user = session.query(User).filter(
                    and_(User.id == user_id, User.is_active == True)
                ).first()
                
                return user
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من الرمز المميز: {e}")
            return None
    
    def check_permission(self, user: User, required_role: str) -> bool:
        """
        التحقق من صلاحيات المستخدم
        
        Args:
            user: المستخدم
            required_role: الدور المطلوب
            
        Returns:
            True إذا كان المستخدم يملك الصلاحية
        """
        if not user or not user.is_active:
            return False
        
        # ترتيب الأدوار حسب الصلاحيات (من الأعلى للأقل)
        role_hierarchy = {
            "admin": 3,
            "supervisor": 2,
            "member": 1
        }
        
        user_level = role_hierarchy.get(user.role.value, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return user_level >= required_level
    
    def change_password(
        self, 
        user_id: int, 
        old_password: str, 
        new_password: str
    ) -> Dict[str, Any]:
        """
        تغيير كلمة مرور المستخدم
        
        Args:
            user_id: معرف المستخدم
            old_password: كلمة المرور القديمة
            new_password: كلمة المرور الجديدة
            
        Returns:
            نتيجة تغيير كلمة المرور
        """
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter_by(id=user_id, is_active=True).first()
                if not user:
                    return {"success": False, "message": "المستخدم غير موجود"}
                
                # التحقق من كلمة المرور القديمة
                if not verify_password(old_password, user.password_hash):
                    return {"success": False, "message": "كلمة المرور القديمة غير صحيحة"}
                
                # التحقق من قوة كلمة المرور الجديدة
                password_check = validate_password_strength(new_password)
                if not password_check["is_valid"]:
                    return {
                        "success": False,
                        "message": "كلمة المرور الجديدة ضعيفة",
                        "errors": password_check["errors"]
                    }
                
                # تحديث كلمة المرور
                user.password_hash = hash_password(new_password)
                user.updated_at = datetime.utcnow()
                session.commit()
                
                logger.info(f"تم تغيير كلمة مرور المستخدم: {user.username}")
                
                return {"success": True, "message": "تم تغيير كلمة المرور بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في تغيير كلمة المرور: {e}")
            return {"success": False, "message": f"خطأ في تغيير كلمة المرور: {str(e)}"}
    
    def update_user_profile(
        self,
        user_id: int,
        full_name: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        تحديث ملف المستخدم الشخصي
        
        Args:
            user_id: معرف المستخدم
            full_name: الاسم الكامل الجديد
            email: البريد الإلكتروني الجديد
            phone: رقم الهاتف الجديد
            
        Returns:
            نتيجة التحديث
        """
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter_by(id=user_id, is_active=True).first()
                if not user:
                    return {"success": False, "message": "المستخدم غير موجود"}
                
                # التحقق من عدم تكرار البريد الإلكتروني
                if email and email != user.email:
                    existing_user = session.query(User).filter(
                        and_(User.email == email, User.id != user_id)
                    ).first()
                    if existing_user:
                        return {"success": False, "message": "البريد الإلكتروني موجود بالفعل"}
                
                # تحديث الحقول
                if full_name:
                    user.full_name = full_name
                if email:
                    user.email = email
                if phone is not None:
                    user.phone = phone
                
                user.updated_at = datetime.utcnow()
                session.commit()
                
                logger.info(f"تم تحديث ملف المستخدم: {user.username}")
                
                return {"success": True, "message": "تم تحديث الملف الشخصي بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في تحديث الملف الشخصي: {e}")
            return {"success": False, "message": f"خطأ في تحديث الملف الشخصي: {str(e)}"}
    
    def get_all_users(self, current_user_id: int) -> List[Dict[str, Any]]:
        """
        الحصول على جميع المستخدمين (للمديرين فقط)
        
        Args:
            current_user_id: معرف المستخدم الحالي
            
        Returns:
            قائمة بالمستخدمين
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من صلاحيات المستخدم
                current_user = session.query(User).filter_by(id=current_user_id).first()
                if not current_user or current_user.role != UserRole.ADMIN:
                    return []
                
                users = session.query(User).filter_by(is_active=True).all()
                
                users_list = []
                for user in users:
                    users_list.append({
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "full_name": user.full_name,
                        "phone": user.phone,
                        "role": user.role.value,
                        "created_at": user.created_at.isoformat(),
                        "updated_at": user.updated_at.isoformat()
                    })
                
                return users_list
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على المستخدمين: {e}")
            return []

# إنشاء مثيل مدير المصادقة
auth_manager = AuthManager()
