{% extends "base.html" %}

{% block title %}إدارة الصناديق - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-folder-open me-2"></i>
        إدارة الصناديق
    </h1>
    <button class="btn btn-primary" onclick="showCreateFundModal()">
        <i class="fas fa-plus me-2"></i>
        إنشاء صندوق جديد
    </button>
</div>

<!-- قائمة الصناديق -->
<div class="row" id="funds_container">
    <!-- الصناديق ستظهر هنا -->
</div>

<!-- مودال إنشاء صندوق جديد -->
<div class="modal fade" id="createFundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء صندوق جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createFundForm">
                    <div class="mb-3">
                        <label class="form-label">اسم الصندوق</label>
                        <input type="text" class="form-control" id="fund_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="fund_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع الصندوق</label>
                        <select class="form-control" id="fund_type" required>
                            <option value="">اختر النوع</option>
                            <option value="family">عائلي</option>
                            <option value="individual">فردي</option>
                            <option value="personal">شخصي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة مرور الصندوق (اختيارية)</label>
                        <input type="password" class="form-control" id="fund_password">
                        <small class="form-text text-muted">لحماية الصندوق من الوصول غير المصرح</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createFund()">إنشاء الصندوق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل الصندوق -->
<div class="modal fade" id="fundDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الصندوق</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="fund_details_content">
                <!-- تفاصيل الصندوق ستظهر هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل الصناديق
function loadFunds() {
    fetch('/api/funds')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayFunds(result.data);
        } else {
            showAlert('خطأ في تحميل الصناديق: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل الصناديق', 'danger');
    });
}

// عرض الصناديق
function displayFunds(funds) {
    const container = document.getElementById('funds_container');
    
    if (funds.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center text-muted py-5">
                    <i class="fas fa-folder-open fa-4x mb-3"></i>
                    <h4>لا توجد صناديق حتى الآن</h4>
                    <p>ابدأ بإنشاء صندوق جديد</p>
                    <button class="btn btn-primary" onclick="showCreateFundModal()">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء صندوق جديد
                    </button>
                </div>
            </div>
        `;
        return;
    }
    
    let html = '';
    funds.forEach(fund => {
        const typeIcon = getFundTypeIcon(fund.fund_type);
        const typeText = getFundTypeText(fund.fund_type);
        
        html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="${typeIcon} me-2"></i>
                            <strong>${fund.name}</strong>
                        </div>
                        <span class="badge bg-primary">${typeText}</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">${fund.description || 'لا يوجد وصف'}</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6 class="text-muted">الرصيد الإجمالي</h6>
                                <h5 class="text-success">${fund.total_balance.toFixed(2)} ريال</h5>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted">عدد الأعضاء</h6>
                                <h5 class="text-info">${fund.members_count || 0}</h5>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-primary btn-sm" onclick="viewFundDetails(${fund.id})">
                                <i class="fas fa-eye me-1"></i>
                                تفاصيل
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="manageFundMembers(${fund.id})">
                                <i class="fas fa-users me-1"></i>
                                الأعضاء
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="viewFundTransactions(${fund.id})">
                                <i class="fas fa-exchange-alt me-1"></i>
                                العمليات
                            </button>
                        </div>
                        <small class="text-muted d-block mt-2">
                            تم الإنشاء: ${new Date(fund.created_at).toLocaleDateString('ar-SA')}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// الحصول على أيقونة نوع الصندوق
function getFundTypeIcon(type) {
    switch(type) {
        case 'family': return 'fas fa-home';
        case 'individual': return 'fas fa-user';
        case 'personal': return 'fas fa-user-circle';
        default: return 'fas fa-folder';
    }
}

// الحصول على نص نوع الصندوق
function getFundTypeText(type) {
    switch(type) {
        case 'family': return 'عائلي';
        case 'individual': return 'فردي';
        case 'personal': return 'شخصي';
        default: return 'غير محدد';
    }
}

// إظهار مودال إنشاء صندوق
function showCreateFundModal() {
    document.getElementById('createFundForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('createFundModal'));
    modal.show();
}

// إنشاء صندوق جديد
function createFund() {
    const name = document.getElementById('fund_name').value;
    const description = document.getElementById('fund_description').value;
    const fundType = document.getElementById('fund_type').value;
    const password = document.getElementById('fund_password').value;
    
    if (!name || !fundType) {
        showAlert('يرجى إدخال اسم الصندوق ونوعه', 'warning');
        return;
    }
    
    const data = {
        name: name,
        description: description,
        fund_type: fundType,
        password: password
    };
    
    fetch('/api/funds', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('createFundModal')).hide();
            showAlert('تم إنشاء الصندوق بنجاح', 'success');
            loadFunds();
        } else {
            showAlert('خطأ في إنشاء الصندوق: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إنشاء الصندوق', 'danger');
    });
}

// عرض تفاصيل الصندوق
function viewFundDetails(fundId) {
    fetch(`/api/funds/${fundId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayFundDetails(result.data);
            const modal = new bootstrap.Modal(document.getElementById('fundDetailsModal'));
            modal.show();
        } else {
            showAlert('خطأ في تحميل تفاصيل الصندوق', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل التفاصيل', 'danger');
    });
}

// عرض تفاصيل الصندوق في المودال
function displayFundDetails(fund) {
    const content = document.getElementById('fund_details_content');
    const typeIcon = getFundTypeIcon(fund.fund_type);
    const typeText = getFundTypeText(fund.fund_type);
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات أساسية</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>الاسم:</strong></td>
                        <td>${fund.name}</td>
                    </tr>
                    <tr>
                        <td><strong>النوع:</strong></td>
                        <td><i class="${typeIcon} me-2"></i>${typeText}</td>
                    </tr>
                    <tr>
                        <td><strong>الوصف:</strong></td>
                        <td>${fund.description || 'لا يوجد وصف'}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإنشاء:</strong></td>
                        <td>${new Date(fund.created_at).toLocaleDateString('ar-SA')}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>الإحصائيات</h6>
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6>الرصيد الإجمالي</h6>
                                <h4>${fund.total_balance.toFixed(2)} ريال</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h6>عدد الأعضاء</h6>
                                <h4>${fund.members_count || 0}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// إدارة أعضاء الصندوق
function manageFundMembers(fundId) {
    window.location.href = `/members?fund_id=${fundId}`;
}

// عرض عمليات الصندوق
function viewFundTransactions(fundId) {
    window.location.href = `/transactions?fund_id=${fundId}`;
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadFunds();
});
</script>
{% endblock %}
