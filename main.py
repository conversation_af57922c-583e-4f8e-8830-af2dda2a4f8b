#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لصندوق التوفير
تطبيق شامل لإدارة الصناديق والأعضاء والعمليات المالية
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kivy.config import Config
from kivy.core.window import Window
from kivy.utils import platform

# إعدادات Kivy
Config.set('graphics', 'resizable', True)
Config.set('graphics', 'minimum_width', '800')
Config.set('graphics', 'minimum_height', '600')

# تعيين حجم النافذة للكمبيوتر
if platform not in ('android', 'ios'):
    Window.size = (1200, 800)

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.textfield import MDTextField
from kivymd.uix.label import MDLabel
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.navigationdrawer import MDNavigationDrawer, MDNavigationDrawerMenu
from kivymd.uix.list import MDList, OneLineListItem
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar import Snackbar
from kivymd.theming import ThemableBehavior
from kivymd.uix.behaviors import RectangularElevationBehavior

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.lang import Builder
import logging

# استيراد مدراء النظام
from src.auth.auth_manager import auth_manager
from src.core.fund_manager import fund_manager
from src.core.member_manager import member_manager
from src.core.family_manager import family_manager
from src.core.transaction_manager import transaction_manager

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# تصميم KV للواجهة
KV = '''
#:import Window kivy.core.window.Window

<LoginScreen>:
    name: "login"
    md_bg_color: app.theme_cls.bg_light
    
    MDBoxLayout:
        orientation: "vertical"
        adaptive_height: True
        pos_hint: {"center_x": 0.5, "center_y": 0.5}
        size_hint_x: 0.8
        spacing: dp(20)
        
        MDCard:
            elevation: 10
            padding: dp(30)
            size_hint_y: None
            height: dp(400)
            md_bg_color: app.theme_cls.bg_normal
            
            MDBoxLayout:
                orientation: "vertical"
                spacing: dp(20)
                
                MDLabel:
                    text: "صندوق التوفير"
                    theme_text_color: "Primary"
                    font_style: "H4"
                    halign: "center"
                    size_hint_y: None
                    height: dp(50)
                
                MDTextField:
                    id: username_field
                    hint_text: "اسم المستخدم"
                    helper_text: "أدخل اسم المستخدم أو البريد الإلكتروني"
                    helper_text_mode: "on_focus"
                    icon_right: "account"
                    size_hint_y: None
                    height: dp(60)
                
                MDTextField:
                    id: password_field
                    hint_text: "كلمة المرور"
                    helper_text: "أدخل كلمة المرور"
                    helper_text_mode: "on_focus"
                    icon_right: "lock"
                    password: True
                    size_hint_y: None
                    height: dp(60)
                
                MDRaisedButton:
                    text: "تسجيل الدخول"
                    size_hint_y: None
                    height: dp(50)
                    md_bg_color: app.theme_cls.primary_color
                    on_release: root.login()
                
                MDRaisedButton:
                    text: "إنشاء حساب جديد"
                    size_hint_y: None
                    height: dp(50)
                    md_bg_color: app.theme_cls.accent_color
                    on_release: root.show_register_dialog()

<MainScreen>:
    name: "main"
    
    MDBoxLayout:
        orientation: "vertical"
        
        MDTopAppBar:
            title: "صندوق التوفير"
            elevation: 4
            left_action_items: [["menu", lambda x: nav_drawer.set_state("open")]]
            right_action_items: [["logout", lambda x: root.logout()]]
        
        MDNavigationLayout:
            
            MDScreenManager:
                id: screen_manager
                
                MDScreen:
                    name: "dashboard"
                    
                    MDScrollView:
                        
                        MDBoxLayout:
                            orientation: "vertical"
                            adaptive_height: True
                            spacing: dp(20)
                            padding: dp(20)
                            
                            MDLabel:
                                text: "لوحة التحكم"
                                theme_text_color: "Primary"
                                font_style: "H5"
                                size_hint_y: None
                                height: dp(40)
                            
                            MDGridLayout:
                                cols: 2
                                spacing: dp(20)
                                adaptive_height: True
                                
                                MDCard:
                                    elevation: 5
                                    padding: dp(20)
                                    size_hint_y: None
                                    height: dp(150)
                                    md_bg_color: app.theme_cls.primary_color
                                    
                                    MDBoxLayout:
                                        orientation: "vertical"
                                        
                                        MDLabel:
                                            text: "الصناديق"
                                            theme_text_color: "Custom"
                                            text_color: 1, 1, 1, 1
                                            font_style: "H6"
                                            halign: "center"
                                        
                                        MDLabel:
                                            id: funds_count
                                            text: "0"
                                            theme_text_color: "Custom"
                                            text_color: 1, 1, 1, 1
                                            font_style: "H4"
                                            halign: "center"
                                
                                MDCard:
                                    elevation: 5
                                    padding: dp(20)
                                    size_hint_y: None
                                    height: dp(150)
                                    md_bg_color: app.theme_cls.accent_color
                                    
                                    MDBoxLayout:
                                        orientation: "vertical"
                                        
                                        MDLabel:
                                            text: "الأعضاء"
                                            theme_text_color: "Custom"
                                            text_color: 1, 1, 1, 1
                                            font_style: "H6"
                                            halign: "center"
                                        
                                        MDLabel:
                                            id: members_count
                                            text: "0"
                                            theme_text_color: "Custom"
                                            text_color: 1, 1, 1, 1
                                            font_style: "H4"
                                            halign: "center"
            
            MDNavigationDrawer:
                id: nav_drawer
                radius: (0, 16, 16, 0)
                
                MDNavigationDrawerMenu:
                    
                    MDNavigationDrawerHeader:
                        title: "صندوق التوفير"
                        title_color: "#4a4939"
                        text: "إدارة الصناديق والأعضاء"
                        spacing: "4dp"
                        source: "src/ui/assets/images/logo.png"
                    
                    MDNavigationDrawerLabel:
                        text: "القوائم الرئيسية"
                    
                    DrawerClickableItem:
                        icon: "view-dashboard"
                        text: "لوحة التحكم"
                        on_release: root.switch_screen("dashboard")
                    
                    DrawerClickableItem:
                        icon: "folder-multiple"
                        text: "الصناديق"
                        on_release: root.switch_screen("funds")
                    
                    DrawerClickableItem:
                        icon: "account-group"
                        text: "الأعضاء"
                        on_release: root.switch_screen("members")
                    
                    DrawerClickableItem:
                        icon: "home-group"
                        text: "العائلات"
                        on_release: root.switch_screen("families")
                    
                    DrawerClickableItem:
                        icon: "cash-multiple"
                        text: "العمليات المالية"
                        on_release: root.switch_screen("transactions")
                    
                    MDNavigationDrawerDivider:
                    
                    MDNavigationDrawerLabel:
                        text: "التقارير والخدمات"
                    
                    DrawerClickableItem:
                        icon: "chart-line"
                        text: "التقارير"
                        on_release: root.switch_screen("reports")
                    
                    DrawerClickableItem:
                        icon: "tools"
                        text: "الخدمات"
                        on_release: root.switch_screen("services")
                    
                    DrawerClickableItem:
                        icon: "cog"
                        text: "الإعدادات"
                        on_release: root.switch_screen("settings")

<DrawerClickableItem@MDNavigationDrawerItem>:
    theme_icon_color: "Custom"
    icon_color: "#4a4939"
    ripple_color: "#c5bdd2"
    selected_color: "#0c6c4d"
    text_color: "#4a4939"
    focus_color: "#e7e4c0"
'''

class LoginScreen(MDScreen):
    """شاشة تسجيل الدخول"""
    
    def login(self):
        """تسجيل دخول المستخدم"""
        username = self.ids.username_field.text.strip()
        password = self.ids.password_field.text.strip()
        
        if not username or not password:
            self.show_snackbar("يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # محاولة تسجيل الدخول
        result = auth_manager.login(username, password)
        
        if result["success"]:
            self.show_snackbar("تم تسجيل الدخول بنجاح")
            # الانتقال للشاشة الرئيسية
            self.manager.current = "main"
        else:
            self.show_snackbar(result["message"])
    
    def show_register_dialog(self):
        """عرض نافذة التسجيل"""
        # TODO: إضافة نافذة التسجيل
        self.show_snackbar("ميزة التسجيل قيد التطوير")
    
    def show_snackbar(self, message):
        """عرض رسالة سريعة"""
        Snackbar(text=message).open()

class MainScreen(MDScreen):
    """الشاشة الرئيسية"""
    
    def on_enter(self):
        """عند دخول الشاشة"""
        self.update_dashboard()
    
    def update_dashboard(self):
        """تحديث لوحة التحكم"""
        if auth_manager.current_user:
            # تحديث عدد الصناديق
            funds = fund_manager.get_user_funds(auth_manager.current_user.id)
            self.ids.funds_count.text = str(len(funds))
            
            # تحديث عدد الأعضاء
            total_members = 0
            for fund in funds:
                members = member_manager.get_fund_members(fund["id"])
                total_members += len(members)
            
            self.ids.members_count.text = str(total_members)
    
    def switch_screen(self, screen_name):
        """تبديل الشاشة"""
        self.ids.screen_manager.current = screen_name
        self.ids.nav_drawer.set_state("close")
    
    def logout(self):
        """تسجيل الخروج"""
        auth_manager.logout()
        self.manager.current = "login"
        self.show_snackbar("تم تسجيل الخروج بنجاح")
    
    def show_snackbar(self, message):
        """عرض رسالة سريعة"""
        Snackbar(text=message).open()

class TawfeerApp(MDApp):
    """التطبيق الرئيسي"""
    
    def build(self):
        """بناء التطبيق"""
        # إعداد المظهر
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Teal"
        self.theme_cls.accent_palette = "Orange"
        
        # تحميل التصميم
        Builder.load_string(KV)
        
        # إنشاء مدير الشاشات
        sm = MDScreenManager()
        
        # إضافة الشاشات
        sm.add_widget(LoginScreen())
        sm.add_widget(MainScreen())
        
        # البدء بشاشة تسجيل الدخول
        sm.current = "login"
        
        return sm
    
    def on_start(self):
        """عند بدء التطبيق"""
        logger.info("تم بدء تطبيق صندوق التوفير")
        
        # إنشاء المجلدات المطلوبة
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data/backups", exist_ok=True)
        os.makedirs("data/exports", exist_ok=True)
    
    def on_stop(self):
        """عند إغلاق التطبيق"""
        logger.info("تم إغلاق تطبيق صندوق التوفير")

if __name__ == "__main__":
    TawfeerApp().run()
