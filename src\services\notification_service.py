#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة الإشعارات والملاحظات
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime, date, timedelta
from enum import Enum
import logging

from ..database.models import Note
from ..database.database import db_manager

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """أنواع الإشعارات"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"
    REMINDER = "reminder"

class NotificationPriority(Enum):
    """أولوية الإشعارات"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class NotificationService:
    """خدمة الإشعارات والملاحظات"""
    
    def __init__(self):
        """تهيئة خدمة الإشعارات"""
        pass
    
    def create_notification(
        self,
        title: str,
        message: str,
        notification_type: NotificationType = NotificationType.INFO,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        user_id: Optional[int] = None,
        fund_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        إنشاء إشعار جديد (مبسط)

        Args:
            title: عنوان الإشعار
            message: نص الإشعار
            notification_type: نوع الإشعار
            priority: أولوية الإشعار
            user_id: معرف المستخدم المستهدف
            fund_id: معرف الصندوق

        Returns:
            نتيجة إنشاء الإشعار
        """
        try:
            # في الوقت الحالي، سنحفظ الإشعارات في الذاكرة فقط
            # يمكن تطوير هذا لاحقاً لحفظها في قاعدة البيانات

            logger.info(f"تم إنشاء إشعار جديد: {title}")

            return {
                "success": True,
                "message": "تم إنشاء الإشعار بنجاح",
                "notification_id": 1
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء الإشعار: {e}")
            return {"success": False, "message": f"خطأ في إنشاء الإشعار: {str(e)}"}
    
    def get_user_notifications(
        self,
        user_id: int,
        unread_only: bool = False,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        الحصول على إشعارات المستخدم (مبسط)

        Args:
            user_id: معرف المستخدم
            unread_only: إشعارات غير مقروءة فقط
            limit: عدد الإشعارات المطلوبة

        Returns:
            قائمة الإشعارات
        """
        try:
            # إشعارات تجريبية
            notifications = [
                {
                    "id": 1,
                    "title": "مرحباً بك في صندوق التوفير",
                    "message": "تم تسجيل دخولك بنجاح",
                    "type": "success",
                    "priority": "medium",
                    "is_read": False,
                    "created_at": datetime.now().isoformat(),
                    "action_url": "/dashboard",
                    "extra_data": None
                }
            ]

            return notifications

        except Exception as e:
            logger.error(f"خطأ في الحصول على الإشعارات: {e}")
            return []
    
    def mark_notification_as_read(self, notification_id: int, user_id: int) -> Dict[str, Any]:
        """
        تمييز الإشعار كمقروء (مبسط)

        Args:
            notification_id: معرف الإشعار
            user_id: معرف المستخدم

        Returns:
            نتيجة العملية
        """
        try:
            # في الوقت الحالي، نعيد نجاح العملية فقط
            return {"success": True, "message": "تم تمييز الإشعار كمقروء"}

        except Exception as e:
            logger.error(f"خطأ في تمييز الإشعار: {e}")
            return {"success": False, "message": f"خطأ في العملية: {str(e)}"}
    
    def create_note(
        self,
        title: str,
        content: str,
        user_id: int,
        color: str = "#ffffff",
        fund_id: Optional[int] = None,
        member_id: Optional[int] = None,
        reminder_at: Optional[datetime] = None,
        is_pinned: bool = False
    ) -> Dict[str, Any]:
        """
        إنشاء ملاحظة جديدة
        
        Args:
            title: عنوان الملاحظة
            content: محتوى الملاحظة
            user_id: معرف المستخدم
            color: لون الملاحظة
            fund_id: معرف الصندوق
            member_id: معرف العضو
            reminder_at: وقت التذكير
            is_pinned: مثبتة أم لا
            
        Returns:
            نتيجة إنشاء الملاحظة
        """
        try:
            with db_manager.get_session() as session:
                note = Note(
                    title=title,
                    content=content,
                    user_id=user_id,
                    color=color,
                    fund_id=fund_id,
                    member_id=member_id,
                    reminder_at=reminder_at,
                    is_pinned=is_pinned
                )
                
                session.add(note)
                session.commit()
                session.refresh(note)
                
                # إنشاء تذكير إذا كان مطلوباً
                if reminder_at:
                    self.create_notification(
                        title=f"تذكير: {title}",
                        message=content,
                        notification_type=NotificationType.REMINDER,
                        user_id=user_id,
                        scheduled_at=reminder_at,
                        action_url=f"/notes/{note.id}"
                    )
                
                logger.info(f"تم إنشاء ملاحظة جديدة: {title}")
                
                return {
                    "success": True,
                    "message": "تم إنشاء الملاحظة بنجاح",
                    "note_id": note.id
                }
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء الملاحظة: {e}")
            return {"success": False, "message": f"خطأ في إنشاء الملاحظة: {str(e)}"}
    
    def get_user_notes(
        self,
        user_id: int,
        fund_id: Optional[int] = None,
        archived: bool = False,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        الحصول على ملاحظات المستخدم
        
        Args:
            user_id: معرف المستخدم
            fund_id: معرف الصندوق (اختياري)
            archived: الملاحظات المؤرشفة
            limit: عدد الملاحظات
            
        Returns:
            قائمة الملاحظات
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(Note).filter(
                    and_(
                        Note.user_id == user_id,
                        Note.is_active == True,
                        Note.is_archived == archived
                    )
                )
                
                if fund_id:
                    query = query.filter(Note.fund_id == fund_id)
                
                notes = query.order_by(
                    desc(Note.is_pinned),
                    desc(Note.updated_at)
                ).limit(limit).all()
                
                result = []
                for note in notes:
                    result.append({
                        "id": note.id,
                        "title": note.title,
                        "content": note.content,
                        "color": note.color,
                        "is_pinned": note.is_pinned,
                        "is_archived": note.is_archived,
                        "created_at": note.created_at.isoformat(),
                        "updated_at": note.updated_at.isoformat(),
                        "reminder_at": note.reminder_at.isoformat() if note.reminder_at else None,
                        "fund_id": note.fund_id,
                        "member_id": note.member_id
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على الملاحظات: {e}")
            return []
    
    def update_note(
        self,
        note_id: int,
        user_id: int,
        title: Optional[str] = None,
        content: Optional[str] = None,
        color: Optional[str] = None,
        is_pinned: Optional[bool] = None,
        reminder_at: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        تحديث ملاحظة
        
        Args:
            note_id: معرف الملاحظة
            user_id: معرف المستخدم
            title: العنوان الجديد
            content: المحتوى الجديد
            color: اللون الجديد
            is_pinned: حالة التثبيت
            reminder_at: وقت التذكير الجديد
            
        Returns:
            نتيجة التحديث
        """
        try:
            with db_manager.get_session() as session:
                note = session.query(Note).filter(
                    and_(Note.id == note_id, Note.user_id == user_id, Note.is_active == True)
                ).first()
                
                if not note:
                    return {"success": False, "message": "الملاحظة غير موجودة"}
                
                # تحديث الحقول
                if title is not None:
                    note.title = title
                if content is not None:
                    note.content = content
                if color is not None:
                    note.color = color
                if is_pinned is not None:
                    note.is_pinned = is_pinned
                if reminder_at is not None:
                    note.reminder_at = reminder_at
                
                note.updated_at = datetime.utcnow()
                session.commit()
                
                return {"success": True, "message": "تم تحديث الملاحظة بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في تحديث الملاحظة: {e}")
            return {"success": False, "message": f"خطأ في التحديث: {str(e)}"}
    
    def delete_note(self, note_id: int, user_id: int) -> Dict[str, Any]:
        """
        حذف ملاحظة
        
        Args:
            note_id: معرف الملاحظة
            user_id: معرف المستخدم
            
        Returns:
            نتيجة الحذف
        """
        try:
            with db_manager.get_session() as session:
                note = session.query(Note).filter(
                    and_(Note.id == note_id, Note.user_id == user_id)
                ).first()
                
                if not note:
                    return {"success": False, "message": "الملاحظة غير موجودة"}
                
                note.is_active = False
                session.commit()
                
                return {"success": True, "message": "تم حذف الملاحظة بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في حذف الملاحظة: {e}")
            return {"success": False, "message": f"خطأ في الحذف: {str(e)}"}

# إنشاء مثيل خدمة الإشعارات
notification_service = NotificationService()
