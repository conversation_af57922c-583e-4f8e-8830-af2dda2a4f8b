{% extends "base.html" %}

{% block title %}التقارير - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-chart-bar me-2"></i>
        التقارير والإحصائيات
    </h1>
</div>

<!-- اختيار الصندوق -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    اختيار الصندوق
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="fund_select" class="form-label">الصندوق</label>
                        <select class="form-control" id="fund_select" onchange="loadFundData()">
                            <option value="">اختر الصندوق</option>
                            {% for fund in funds %}
                            <option value="{{ fund.id }}">{{ fund.name }} ({{ fund.fund_type }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="report_type" class="form-label">نوع التقرير</label>
                        <select class="form-control" id="report_type" onchange="updateDateFields()">
                            <option value="daily">يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly" selected>شهري</option>
                            <option value="yearly">سنوي</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mt-3" id="date_fields">
                    <div class="col-md-6">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date">
                    </div>
                    <div class="col-md-6">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date">
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <button class="btn btn-primary me-2" onclick="generateReport()">
                            <i class="fas fa-chart-line me-2"></i>
                            إنشاء التقرير
                        </button>
                        <button class="btn btn-success me-2" onclick="exportReport('html')">
                            <i class="fas fa-file-code me-2"></i>
                            تصدير HTML
                        </button>
                        <button class="btn btn-warning" onclick="exportReport('csv')">
                            <i class="fas fa-file-csv me-2"></i>
                            تصدير CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- عرض التقرير -->
<div class="row" id="report_container" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    نتائج التقرير
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
            <div class="card-body" id="report_content">
                <!-- سيتم ملء المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<!-- التقارير السريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    التقارير السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-day fa-2x mb-2"></i>
                                <h6>تقرير اليوم</h6>
                                <button class="btn btn-light btn-sm" onclick="quickReport('daily')">
                                    إنشاء
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-week fa-2x mb-2"></i>
                                <h6>تقرير الأسبوع</h6>
                                <button class="btn btn-light btn-sm" onclick="quickReport('weekly')">
                                    إنشاء
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <h6>تقرير الشهر</h6>
                                <button class="btn btn-light btn-sm" onclick="quickReport('monthly')">
                                    إنشاء
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h6>تقرير السنة</h6>
                                <button class="btn btn-light btn-sm" onclick="quickReport('yearly')">
                                    إنشاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث حقول التاريخ حسب نوع التقرير
function updateDateFields() {
    const reportType = document.getElementById('report_type').value;
    const dateFields = document.getElementById('date_fields');
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (reportType === 'custom') {
        dateFields.style.display = 'block';
        startDate.required = true;
        endDate.required = true;
    } else if (reportType === 'daily' || reportType === 'weekly') {
        dateFields.style.display = 'block';
        startDate.required = true;
        endDate.required = false;
        endDate.style.display = 'none';
    } else {
        dateFields.style.display = 'block';
        startDate.required = false;
        endDate.required = false;
    }
}

// إنشاء التقرير
function generateReport() {
    const fundId = document.getElementById('fund_select').value;
    const reportType = document.getElementById('report_type').value;
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (!fundId) {
        alert('يرجى اختيار الصندوق');
        return;
    }
    
    // بناء URL
    let url = `/api/reports/fund/${fundId}?report_type=${reportType}`;
    if (startDate) url += `&start_date=${startDate}`;
    if (endDate) url += `&end_date=${endDate}`;
    
    // عرض مؤشر التحميل
    document.getElementById('report_content').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري إنشاء التقرير...</p>
        </div>
    `;
    document.getElementById('report_container').style.display = 'block';
    
    // طلب التقرير
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayReport(data);
            } else {
                document.getElementById('report_content').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('report_content').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ في إنشاء التقرير
                </div>
            `;
        });
}

// عرض التقرير
function displayReport(data) {
    const fundInfo = data.fund_info;
    const period = data.period;
    const financial = data.financial_summary;
    const members = data.members_summary;
    
    let html = `
        <div class="report-header mb-4">
            <h4 class="text-center">${fundInfo.name}</h4>
            <p class="text-center text-muted">
                من ${period.start_date} إلى ${period.end_date}
                (${period.days_count} يوم)
            </p>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h6>إجمالي الإيرادات</h6>
                        <h4>${financial.total_income.toFixed(2)} ريال</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h6>إجمالي المصروفات</h6>
                        <h4>${financial.total_expense.toFixed(2)} ريال</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h6>السحوبات</h6>
                        <h4>${financial.total_withdrawals.toFixed(2)} ريال</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h6>السلفيات</h6>
                        <h4>${financial.total_loans.toFixed(2)} ريال</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h6>صافي الرصيد</h6>
                        <h4>${financial.net_balance.toFixed(2)} ريال</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم العضو</th>
                        <th>الرصيد الحالي</th>
                        <th>إيرادات الفترة</th>
                        <th>مصروفات الفترة</th>
                        <th>عدد العمليات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    members.forEach(member => {
        html += `
            <tr>
                <td>${member.name}</td>
                <td>${member.current_balance.toFixed(2)} ريال</td>
                <td class="text-success">${member.period_income.toFixed(2)} ريال</td>
                <td class="text-danger">${member.period_expense.toFixed(2)} ريال</td>
                <td>${member.transactions_count}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">
                تم إنشاء التقرير في: ${new Date(data.generated_at).toLocaleString('ar-SA')}
            </small>
        </div>
    `;
    
    document.getElementById('report_content').innerHTML = html;
}

// تصدير التقرير
function exportReport(format) {
    const fundId = document.getElementById('fund_select').value;
    const reportType = document.getElementById('report_type').value;
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (!fundId) {
        alert('يرجى اختيار الصندوق');
        return;
    }
    
    let url = `/api/reports/export/${fundId}?format=${format}&report_type=${reportType}`;
    if (startDate) url += `&start_date=${startDate}`;
    if (endDate) url += `&end_date=${endDate}`;
    
    window.open(url, '_blank');
}

// التقارير السريعة
function quickReport(type) {
    const fundId = document.getElementById('fund_select').value;
    
    if (!fundId) {
        alert('يرجى اختيار الصندوق أولاً');
        return;
    }
    
    document.getElementById('report_type').value = type;
    updateDateFields();
    generateReport();
}

// طباعة التقرير
function printReport() {
    const content = document.getElementById('report_content').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تقرير الصندوق</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="container">
                ${content}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDateFields();
    
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').value = today;
    document.getElementById('end_date').value = today;
});
</script>
{% endblock %}
