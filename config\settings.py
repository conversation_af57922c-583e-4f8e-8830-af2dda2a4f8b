#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات تطبيق صندوق التوفير
يحتوي على جميع الإعدادات والثوابت المستخدمة في التطبيق
"""

import os
from pathlib import Path
from typing import Dict, Any

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
CONFIG_DIR = PROJECT_ROOT / "config"
BACKUP_DIR = DATA_DIR / "backups"
EXPORTS_DIR = DATA_DIR / "exports"

# إعدادات التطبيق
APP_CONFIG = {
    "name": "صندوق التوفير",
    "version": "1.0.0",
    "description": "تطبيق شامل لإدارة الصناديق والأعضاء والعمليات المالية",
    "author": "فريق التطوير",
    "license": "MIT",
    "website": "https://github.com/your-username/tawfeer-app",
    "support_email": "<EMAIL>"
}

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    "type": "sqlite",
    "path": str(DATA_DIR / "tawfeer.db"),
    "backup_interval": 24,  # ساعات
    "max_backups": 30,      # عدد النسخ الاحتياطية المحفوظة
    "auto_backup": True
}

# إعدادات الأمان
SECURITY_CONFIG = {
    "secret_key": os.getenv("SECRET_KEY", "your-secret-key-change-in-production"),
    "algorithm": "HS256",
    "access_token_expire_minutes": 30,
    "password_min_length": 8,
    "max_login_attempts": 5,
    "lockout_duration": 15,  # دقائق
    "encryption_key_file": str(CONFIG_DIR / "encryption.key")
}

# إعدادات واجهة المستخدم
UI_CONFIG = {
    "theme": "light",           # light, dark
    "language": "ar",           # ar, en
    "direction": "rtl",         # rtl, ltr
    "font_family": "Cairo",
    "primary_color": "#007bff",
    "secondary_color": "#6c757d",
    "success_color": "#28a745",
    "danger_color": "#dc3545",
    "warning_color": "#ffc107",
    "info_color": "#17a2b8"
}

# إعدادات الويب
WEB_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "reload": False,
    "log_level": "info",
    "cors_origins": ["*"],
    "static_dir": str(PROJECT_ROOT / "web" / "static"),
    "templates_dir": str(PROJECT_ROOT / "web" / "templates")
}

# إعدادات التقارير
REPORTS_CONFIG = {
    "default_format": "pdf",   # pdf, excel, csv
    "date_format": "%Y-%m-%d",
    "currency": "ريال",
    "number_format": "ar-SA",
    "max_records_per_report": 10000,
    "export_timeout": 300      # ثواني
}

# إعدادات الإشعارات
NOTIFICATIONS_CONFIG = {
    "enabled": True,
    "email_enabled": False,
    "sms_enabled": False,
    "whatsapp_enabled": False,
    "reminder_days": [1, 3, 7],  # أيام التذكير
    "max_notifications": 100
}

# إعدادات التخزين السحابي
CLOUD_CONFIG = {
    "enabled": False,
    "provider": "local",        # local, aws, google, azure
    "sync_interval": 60,        # دقائق
    "auto_sync": False,
    "encryption": True
}

# إعدادات السجلات
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_path": str(LOGS_DIR / "app.log"),
    "max_file_size": 10 * 1024 * 1024,  # 10 MB
    "backup_count": 5,
    "console_output": True
}

# إعدادات الأداء
PERFORMANCE_CONFIG = {
    "cache_enabled": True,
    "cache_timeout": 300,       # ثواني
    "max_concurrent_users": 100,
    "request_timeout": 30,      # ثواني
    "max_file_size": 50 * 1024 * 1024,  # 50 MB
    "pagination_size": 50
}

# إعدادات التطوير
DEVELOPMENT_CONFIG = {
    "debug": False,
    "testing": False,
    "profiling": False,
    "mock_data": False,
    "auto_reload": False
}

# إعدادات البناء والتصدير
BUILD_CONFIG = {
    "android": {
        "package_name": "com.tawfeer.app",
        "version_code": 1,
        "min_sdk": 21,
        "target_sdk": 30,
        "permissions": [
            "INTERNET",
            "WRITE_EXTERNAL_STORAGE",
            "READ_EXTERNAL_STORAGE"
        ]
    },
    "windows": {
        "executable_name": "TawfeerApp.exe",
        "icon_path": "src/ui/assets/images/icon.ico",
        "include_console": False,
        "one_file": False
    },
    "web": {
        "static_compression": True,
        "minify_assets": True,
        "cache_static": True
    }
}

# دالة للحصول على الإعدادات
def get_config(section: str = None) -> Dict[str, Any]:
    """
    الحصول على الإعدادات
    
    Args:
        section: اسم القسم (اختياري)
        
    Returns:
        الإعدادات المطلوبة
    """
    all_configs = {
        "app": APP_CONFIG,
        "database": DATABASE_CONFIG,
        "security": SECURITY_CONFIG,
        "ui": UI_CONFIG,
        "web": WEB_CONFIG,
        "reports": REPORTS_CONFIG,
        "notifications": NOTIFICATIONS_CONFIG,
        "cloud": CLOUD_CONFIG,
        "logging": LOGGING_CONFIG,
        "performance": PERFORMANCE_CONFIG,
        "development": DEVELOPMENT_CONFIG,
        "build": BUILD_CONFIG
    }
    
    if section:
        return all_configs.get(section, {})
    
    return all_configs

# دالة لتحديث الإعدادات
def update_config(section: str, key: str, value: Any) -> bool:
    """
    تحديث إعداد معين
    
    Args:
        section: اسم القسم
        key: مفتاح الإعداد
        value: القيمة الجديدة
        
    Returns:
        True إذا تم التحديث بنجاح
    """
    try:
        configs = get_config()
        if section in configs and key in configs[section]:
            configs[section][key] = value
            return True
        return False
    except Exception:
        return False

# دالة لحفظ الإعدادات
def save_config(config_data: Dict[str, Any], file_path: str = None) -> bool:
    """
    حفظ الإعدادات في ملف
    
    Args:
        config_data: بيانات الإعدادات
        file_path: مسار الملف (اختياري)
        
    Returns:
        True إذا تم الحفظ بنجاح
    """
    import json
    
    if not file_path:
        file_path = CONFIG_DIR / "user_settings.json"
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False

# دالة لتحميل الإعدادات
def load_config(file_path: str = None) -> Dict[str, Any]:
    """
    تحميل الإعدادات من ملف
    
    Args:
        file_path: مسار الملف (اختياري)
        
    Returns:
        بيانات الإعدادات
    """
    import json
    
    if not file_path:
        file_path = CONFIG_DIR / "user_settings.json"
    
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception:
        pass
    
    return {}

# دالة للتحقق من صحة الإعدادات
def validate_config() -> Dict[str, bool]:
    """
    التحقق من صحة الإعدادات
    
    Returns:
        نتائج التحقق
    """
    results = {}
    
    # التحقق من المجلدات
    required_dirs = [DATA_DIR, LOGS_DIR, CONFIG_DIR, BACKUP_DIR, EXPORTS_DIR]
    for dir_path in required_dirs:
        results[f"dir_{dir_path.name}"] = dir_path.exists()
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                results[f"dir_{dir_path.name}"] = True
            except Exception:
                results[f"dir_{dir_path.name}"] = False
    
    # التحقق من ملف قاعدة البيانات
    db_path = Path(DATABASE_CONFIG["path"])
    results["database_accessible"] = db_path.parent.exists()
    
    # التحقق من مفتاح التشفير
    encryption_key_file = Path(SECURITY_CONFIG["encryption_key_file"])
    results["encryption_key"] = encryption_key_file.exists()
    
    return results

# تهيئة الإعدادات عند الاستيراد
def initialize_config():
    """تهيئة الإعدادات الأساسية"""
    # إنشاء المجلدات المطلوبة
    for dir_path in [DATA_DIR, LOGS_DIR, CONFIG_DIR, BACKUP_DIR, EXPORTS_DIR]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # تحميل إعدادات المستخدم إذا كانت موجودة
    user_config = load_config()
    if user_config:
        # دمج إعدادات المستخدم مع الإعدادات الافتراضية
        for section, settings in user_config.items():
            if section in get_config():
                get_config()[section].update(settings)

# تشغيل التهيئة
initialize_config()
