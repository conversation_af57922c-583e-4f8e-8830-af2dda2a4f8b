#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير العمليات المالية - إدارة جميع العمليات المالية
يحتوي على جميع الوظائف المتعلقة بالإيرادات والمصروفات والسحوبات والسلفيات
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from datetime import datetime, date
import logging

from ..database.models import Transaction, Fund, Member, Family, TransactionType
from ..database.database import db_manager

logger = logging.getLogger(__name__)

class TransactionManager:
    """مدير العمليات المالية"""
    
    def __init__(self):
        """تهيئة مدير العمليات المالية"""
        pass
    
    def add_transaction(
        self,
        fund_id: int,
        amount: float,
        transaction_type: str,
        description: str,
        created_by: int,
        member_id: Optional[int] = None,
        family_ids: Optional[List[int]] = None,
        member_ids: Optional[List[int]] = None,
        distribute_equally: bool = True,
        transaction_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        إضافة عملية مالية جديدة
        
        Args:
            fund_id: معرف الصندوق
            amount: المبلغ
            transaction_type: نوع العملية (income, expense, withdrawal, loan, deposit)
            description: وصف العملية
            created_by: معرف المستخدم الذي أنشأ العملية
            member_id: معرف العضو (للعمليات الفردية)
            family_ids: قائمة معرفات العائلات (للعمليات الجماعية)
            member_ids: قائمة معرفات الأعضاء (للعمليات الجماعية)
            distribute_equally: توزيع المبلغ بالتساوي
            transaction_date: تاريخ العملية
            
        Returns:
            نتيجة إضافة العملية
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من وجود الصندوق
                fund = session.query(Fund).filter_by(id=fund_id, is_active=True).first()
                if not fund:
                    return {"success": False, "message": "الصندوق غير موجود"}
                
                # التحقق من صحة نوع العملية
                try:
                    trans_type = TransactionType(transaction_type)
                except ValueError:
                    return {"success": False, "message": "نوع العملية غير صحيح"}
                
                if not transaction_date:
                    transaction_date = datetime.utcnow()
                
                transactions_created = []
                
                # العملية لعضو واحد
                if member_id:
                    result = self._add_single_member_transaction(
                        session, fund, member_id, amount, trans_type, 
                        description, created_by, transaction_date
                    )
                    if not result["success"]:
                        return result
                    transactions_created.append(result["transaction"])
                
                # العملية لعدة أعضاء
                elif member_ids:
                    result = self._add_multiple_members_transaction(
                        session, fund, member_ids, amount, trans_type,
                        description, created_by, transaction_date, distribute_equally
                    )
                    if not result["success"]:
                        return result
                    transactions_created.extend(result["transactions"])
                
                # العملية لعائلات
                elif family_ids:
                    result = self._add_families_transaction(
                        session, fund, family_ids, amount, trans_type,
                        description, created_by, transaction_date, distribute_equally
                    )
                    if not result["success"]:
                        return result
                    transactions_created.extend(result["transactions"])
                
                # العملية لجميع الأعضاء
                else:
                    result = self._add_all_members_transaction(
                        session, fund, amount, trans_type,
                        description, created_by, transaction_date
                    )
                    if not result["success"]:
                        return result
                    transactions_created.extend(result["transactions"])
                
                session.commit()
                
                logger.info(f"تم إضافة {len(transactions_created)} عملية مالية للصندوق {fund.name}")
                
                return {
                    "success": True,
                    "message": f"تم إضافة {len(transactions_created)} عملية مالية بنجاح",
                    "transactions_count": len(transactions_created),
                    "total_amount": sum(t.amount for t in transactions_created)
                }
                
        except Exception as e:
            logger.error(f"خطأ في إضافة العملية المالية: {e}")
            return {"success": False, "message": f"خطأ في إضافة العملية المالية: {str(e)}"}
    
    def _add_single_member_transaction(
        self, session, fund, member_id, amount, trans_type, 
        description, created_by, transaction_date
    ):
        """إضافة عملية لعضو واحد"""
        member = session.query(Member).filter(
            and_(Member.id == member_id, Member.fund_id == fund.id, Member.is_active == True)
        ).first()
        
        if not member:
            return {"success": False, "message": "العضو غير موجود"}
        
        # إنشاء العملية
        transaction = Transaction(
            amount=amount,
            transaction_type=trans_type,
            description=description,
            fund_id=fund.id,
            member_id=member.id,
            created_by=created_by,
            transaction_date=transaction_date
        )
        
        session.add(transaction)
        
        # تحديث الأرصدة
        self._update_balances(session, member, fund, amount, trans_type)
        
        return {"success": True, "transaction": transaction}
    
    def _add_multiple_members_transaction(
        self, session, fund, member_ids, amount, trans_type,
        description, created_by, transaction_date, distribute_equally
    ):
        """إضافة عملية لعدة أعضاء"""
        members = session.query(Member).filter(
            and_(Member.id.in_(member_ids), Member.fund_id == fund.id, Member.is_active == True)
        ).all()
        
        if not members:
            return {"success": False, "message": "لا يوجد أعضاء صالحون"}
        
        transactions = []
        
        if distribute_equally:
            amount_per_member = amount / len(members)
        else:
            amount_per_member = amount
        
        for member in members:
            transaction = Transaction(
                amount=amount_per_member,
                transaction_type=trans_type,
                description=f"{description} - {member.name}",
                fund_id=fund.id,
                member_id=member.id,
                created_by=created_by,
                transaction_date=transaction_date
            )
            
            session.add(transaction)
            transactions.append(transaction)
            
            # تحديث الأرصدة
            self._update_balances(session, member, fund, amount_per_member, trans_type)
        
        return {"success": True, "transactions": transactions}
    
    def _add_families_transaction(
        self, session, fund, family_ids, amount, trans_type,
        description, created_by, transaction_date, distribute_equally
    ):
        """إضافة عملية لعائلات"""
        families = session.query(Family).filter(
            and_(Family.id.in_(family_ids), Family.fund_id == fund.id)
        ).all()
        
        if not families:
            return {"success": False, "message": "لا توجد عائلات صالحة"}
        
        transactions = []
        
        # الحصول على جميع أعضاء العائلات
        all_members = []
        for family in families:
            family_members = session.query(Member).filter(
                and_(Member.family_id == family.id, Member.is_active == True)
            ).all()
            all_members.extend(family_members)
        
        if not all_members:
            return {"success": False, "message": "لا يوجد أعضاء في العائلات المحددة"}
        
        if distribute_equally:
            amount_per_member = amount / len(all_members)
        else:
            amount_per_member = amount
        
        for member in all_members:
            transaction = Transaction(
                amount=amount_per_member,
                transaction_type=trans_type,
                description=f"{description} - {member.name}",
                fund_id=fund.id,
                member_id=member.id,
                created_by=created_by,
                transaction_date=transaction_date
            )
            
            session.add(transaction)
            transactions.append(transaction)
            
            # تحديث الأرصدة
            self._update_balances(session, member, fund, amount_per_member, trans_type)
        
        return {"success": True, "transactions": transactions}
    
    def _add_all_members_transaction(
        self, session, fund, amount, trans_type,
        description, created_by, transaction_date
    ):
        """إضافة عملية لجميع الأعضاء"""
        members = session.query(Member).filter(
            and_(Member.fund_id == fund.id, Member.is_active == True)
        ).all()
        
        if not members:
            return {"success": False, "message": "لا يوجد أعضاء في الصندوق"}
        
        transactions = []
        amount_per_member = amount / len(members)
        
        for member in members:
            transaction = Transaction(
                amount=amount_per_member,
                transaction_type=trans_type,
                description=f"{description} - {member.name}",
                fund_id=fund.id,
                member_id=member.id,
                created_by=created_by,
                transaction_date=transaction_date
            )
            
            session.add(transaction)
            transactions.append(transaction)
            
            # تحديث الأرصدة
            self._update_balances(session, member, fund, amount_per_member, trans_type)
        
        return {"success": True, "transactions": transactions}
    
    def _update_balances(self, session, member, fund, amount, trans_type):
        """تحديث الأرصدة حسب نوع العملية"""
        if trans_type in [TransactionType.INCOME, TransactionType.DEPOSIT]:
            member.balance += amount
            fund.total_balance += amount

            # تحديث رصيد العائلة إذا كان العضو ينتمي لعائلة
            if member.family_id:
                family = session.query(Family).filter_by(id=member.family_id).first()
                if family:
                    family.total_balance += amount

        elif trans_type in [TransactionType.EXPENSE, TransactionType.WITHDRAWAL, TransactionType.LOAN]:
            member.balance -= amount
            fund.total_balance -= amount

            # تحديث رصيد العائلة إذا كان العضو ينتمي لعائلة
            if member.family_id:
                family = session.query(Family).filter_by(id=member.family_id).first()
                if family:
                    family.total_balance -= amount

        elif trans_type == TransactionType.ADJUSTMENT:
            # تعديل الرصيد - لا نحتاج لتحديث الأرصدة هنا لأنها تتم في adjust_member_balance
            pass
    
    def get_fund_transactions(
        self, 
        fund_id: int, 
        limit: int = 50, 
        offset: int = 0,
        transaction_type: Optional[str] = None,
        member_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """
        الحصول على عمليات الصندوق
        
        Args:
            fund_id: معرف الصندوق
            limit: عدد العمليات المطلوبة
            offset: الإزاحة
            transaction_type: نوع العملية للتصفية
            member_id: معرف العضو للتصفية
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            قائمة بالعمليات المالية
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(Transaction).filter_by(fund_id=fund_id)
                
                # تطبيق المرشحات
                if transaction_type:
                    query = query.filter(Transaction.transaction_type == TransactionType(transaction_type))
                
                if member_id:
                    query = query.filter(Transaction.member_id == member_id)
                
                if start_date:
                    query = query.filter(Transaction.transaction_date >= start_date)
                
                if end_date:
                    query = query.filter(Transaction.transaction_date <= end_date)
                
                # ترتيب وتحديد العدد
                transactions = query.order_by(desc(Transaction.transaction_date)).offset(offset).limit(limit).all()
                
                transactions_list = []
                for transaction in transactions:
                    # الحصول على اسم العضو
                    member_name = None
                    if transaction.member_id:
                        member = session.query(Member).filter_by(id=transaction.member_id).first()
                        if member:
                            member_name = member.name
                    
                    # الحصول على اسم المستخدم الذي أنشأ العملية
                    from ..database.models import User
                    user = session.query(User).filter_by(id=transaction.created_by).first()
                    created_by_name = user.full_name if user else "غير معروف"
                    
                    transactions_list.append({
                        "id": transaction.id,
                        "amount": transaction.amount,
                        "transaction_type": transaction.transaction_type.value,
                        "description": transaction.description,
                        "member_id": transaction.member_id,
                        "member_name": member_name,
                        "created_by": transaction.created_by,
                        "created_by_name": created_by_name,
                        "transaction_date": transaction.transaction_date.isoformat(),
                        "created_at": transaction.created_at.isoformat()
                    })
                
                return transactions_list
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على عمليات الصندوق: {e}")
            return []

# إنشاء مثيل مدير العمليات المالية
transaction_manager = TransactionManager()
