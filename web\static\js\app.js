/**
 * ملف JavaScript الرئيسي لتطبيق صندوق التوفير
 * يحتوي على جميع الوظائف التفاعلية للواجهة
 */

// إعدادات عامة
const APP_CONFIG = {
    apiBaseUrl: '/api',
    animationDuration: 300,
    toastDuration: 3000,
    dateFormat: 'YYYY-MM-DD',
    currency: 'ريال'
};

// فئة إدارة التطبيق
class TawfeerApp {
    constructor() {
        this.init();
    }

    // تهيئة التطبيق
    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.loadUserPreferences();
        console.log('تم تهيئة تطبيق صندوق التوفير');
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            this.onPageLoad();
        });

        // تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });

        // إرسال النماذج
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('ajax-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });

        // النقر على الأزرار
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-loading')) {
                this.showButtonLoading(e.target);
            }
        });
    }

    // تهيئة المكونات
    initializeComponents() {
        this.initializeTooltips();
        this.initializeModals();
        this.initializeCharts();
        this.initializeDatePickers();
    }

    // عند تحميل الصفحة
    onPageLoad() {
        this.showPageAnimation();
        this.updateDateTime();
        this.checkNotifications();
        
        // تحديث التاريخ والوقت كل دقيقة
        setInterval(() => {
            this.updateDateTime();
        }, 60000);
    }

    // عند تغيير حجم النافذة
    onWindowResize() {
        this.adjustLayout();
        this.updateCharts();
    }

    // تحميل تفضيلات المستخدم
    loadUserPreferences() {
        const theme = localStorage.getItem('theme') || 'light';
        const language = localStorage.getItem('language') || 'ar';
        
        this.setTheme(theme);
        this.setLanguage(language);
    }

    // عرض رسوم متحركة للصفحة
    showPageAnimation() {
        const elements = document.querySelectorAll('.fade-in');
        elements.forEach((element, index) => {
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // تحديث التاريخ والوقت
    updateDateTime() {
        const now = new Date();
        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement) {
            dateElement.textContent = this.formatDate(now);
        }
        
        if (timeElement) {
            timeElement.textContent = this.formatTime(now);
        }
    }

    // تنسيق التاريخ
    formatDate(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        };
        return date.toLocaleDateString('ar-SA', options);
    }

    // تنسيق الوقت
    formatTime(date) {
        const options = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        return date.toLocaleTimeString('ar-SA', options);
    }

    // التحقق من التنبيهات
    checkNotifications() {
        this.fetchAPI('/notifications')
            .then(data => {
                if (data.success && data.data.length > 0) {
                    this.showNotifications(data.data);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل التنبيهات:', error);
            });
    }

    // عرض التنبيهات
    showNotifications(notifications) {
        const container = document.getElementById('notifications-container');
        if (!container) return;

        notifications.forEach(notification => {
            this.showToast(notification.message, notification.type);
        });
    }

    // عرض رسالة منبثقة
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        const container = document.getElementById('toast-container') || this.createToastContainer();
        container.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // إزالة التوست بعد انتهاء المدة
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, APP_CONFIG.toastDuration + 1000);
    }

    // إنشاء حاوي التوست
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }

    // معالجة إرسال النماذج
    async handleFormSubmit(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        try {
            this.showButtonLoading(submitButton);
            
            const formData = new FormData(form);
            const url = form.action || window.location.pathname;
            const method = form.method || 'POST';
            
            const response = await fetch(url, {
                method: method,
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showToast(data.message || 'تم الحفظ بنجاح', 'success');
                
                // إعادة تحميل البيانات إذا لزم الأمر
                if (form.dataset.reload) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
                
                // إغلاق النافذة المنبثقة إذا كانت موجودة
                const modal = form.closest('.modal');
                if (modal) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }
                
                // إعادة تعيين النموذج
                form.reset();
                
            } else {
                this.showToast(data.message || 'حدث خطأ', 'danger');
            }
            
        } catch (error) {
            console.error('خطأ في إرسال النموذج:', error);
            this.showToast('حدث خطأ في الاتصال', 'danger');
        } finally {
            this.hideButtonLoading(submitButton, originalText);
        }
    }

    // عرض تحميل الزر
    showButtonLoading(button) {
        const originalText = button.textContent;
        button.dataset.originalText = originalText;
        button.innerHTML = '<span class="loading"></span> جاري التحميل...';
        button.disabled = true;
    }

    // إخفاء تحميل الزر
    hideButtonLoading(button, originalText = null) {
        const text = originalText || button.dataset.originalText || 'حفظ';
        button.textContent = text;
        button.disabled = false;
        delete button.dataset.originalText;
    }

    // طلب API
    async fetchAPI(endpoint, options = {}) {
        const url = `${APP_CONFIG.apiBaseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('خطأ في API:', error);
            throw error;
        }
    }

    // تهيئة التلميحات
    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // تهيئة النوافذ المنبثقة
    initializeModals() {
        const modalElements = document.querySelectorAll('.modal');
        modalElements.forEach(modal => {
            modal.addEventListener('hidden.bs.modal', () => {
                // إعادة تعيين النماذج عند إغلاق النافذة
                const forms = modal.querySelectorAll('form');
                forms.forEach(form => form.reset());
            });
        });
    }

    // تهيئة الرسوم البيانية
    initializeCharts() {
        // TODO: تنفيذ الرسوم البيانية باستخدام Chart.js
        console.log('تهيئة الرسوم البيانية...');
    }

    // تهيئة منتقي التاريخ
    initializeDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            // إضافة تنسيق عربي للتاريخ
            input.addEventListener('change', (e) => {
                console.log('تم تغيير التاريخ:', e.target.value);
            });
        });
    }

    // تعديل التخطيط
    adjustLayout() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (window.innerWidth < 768) {
            sidebar?.classList.add('mobile');
            mainContent?.classList.add('mobile');
        } else {
            sidebar?.classList.remove('mobile');
            mainContent?.classList.remove('mobile');
        }
    }

    // تحديث الرسوم البيانية
    updateCharts() {
        // TODO: تحديث الرسوم البيانية عند تغيير حجم النافذة
        console.log('تحديث الرسوم البيانية...');
    }

    // تعيين المظهر
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
    }

    // تعيين اللغة
    setLanguage(language) {
        document.documentElement.setAttribute('lang', language);
        document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');
        localStorage.setItem('language', language);
    }

    // تنسيق العملة
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 2
        }).format(amount);
    }

    // تنسيق الأرقام
    formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }

    // التحقق من صحة البريد الإلكتروني
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // التحقق من صحة رقم الهاتف
    validatePhone(phone) {
        const re = /^[+]?[0-9\s\-\(\)]{10,}$/;
        return re.test(phone);
    }
}

// دوال مساعدة عامة
const Utils = {
    // تأخير التنفيذ
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // تنظيف النص
    sanitizeText(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // نسخ النص للحافظة
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            app.showToast('تم نسخ النص', 'success');
        } catch (err) {
            console.error('فشل في نسخ النص:', err);
            app.showToast('فشل في نسخ النص', 'danger');
        }
    },

    // تحميل ملف
    downloadFile(data, filename, type = 'text/plain') {
        const blob = new Blob([data], { type });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
};

// تهيئة التطبيق عند تحميل الصفحة
const app = new TawfeerApp();

// تصدير للاستخدام العام
window.TawfeerApp = TawfeerApp;
window.Utils = Utils;
window.app = app;
