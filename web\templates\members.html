{% extends "base.html" %}

{% block title %}إدارة الأعضاء - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-users me-2"></i>
        إدارة الأعضاء
    </h1>
    <div>
        <button class="btn btn-success me-2" onclick="showImportModal()">
            <i class="fas fa-file-import me-2"></i>
            استيراد أعضاء
        </button>
        <button class="btn btn-primary" onclick="showAddMemberModal()">
            <i class="fas fa-user-plus me-2"></i>
            إضافة عضو
        </button>
    </div>
</div>

<!-- اختيار الصندوق -->
<div class="row mb-4">
    <div class="col-md-6">
        <label for="fund_select" class="form-label">اختر الصندوق</label>
        <select class="form-control" id="fund_select" onchange="loadMembers()">
            <option value="">اختر الصندوق</option>
            {% for fund in funds %}
            <option value="{{ fund.id }}">{{ fund.name }} ({{ fund.fund_type }})</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-6">
        <label for="search_members" class="form-label">البحث في الأعضاء</label>
        <input type="text" class="form-control" id="search_members" placeholder="ابحث بالاسم أو الجوال" onkeyup="filterMembers()">
    </div>
</div>

<!-- جدول الأعضاء -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الأعضاء
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped" id="members_table">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>الجوال</th>
                        <th>البريد الإلكتروني</th>
                        <th>الرصيد</th>
                        <th>العائلة</th>
                        <th>تاريخ الانضمام</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="members_tbody">
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            يرجى اختيار صندوق لعرض الأعضاء
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- مودال إضافة عضو -->
<div class="modal fade" id="addMemberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عضو جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMemberForm">
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="member_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الجوال</label>
                        <input type="tel" class="form-control" id="member_phone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="member_email">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرصيد الأولي</label>
                        <input type="number" class="form-control" id="member_balance" value="0" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العائلة (اختياري)</label>
                        <select class="form-control" id="member_family">
                            <option value="">بدون عائلة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="member_notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addMember()">إضافة العضو</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال استيراد الأعضاء -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد الأعضاء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">اختر ملف الاستيراد</label>
                    <input type="file" class="form-control" id="import_file" accept=".txt,.csv">
                    <small class="form-text text-muted">
                        يدعم ملفات .txt و .csv<br>
                        تنسيق الملف: الاسم,الجوال,البريد,الرصيد
                    </small>
                </div>
                <div class="mb-3">
                    <label class="form-label">أو أدخل البيانات يدوياً</label>
                    <textarea class="form-control" id="manual_import" rows="6" placeholder="أحمد محمد,0501234567,<EMAIL>,1000
فاطمة علي,0509876543,<EMAIL>,500"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="importMembers()">استيراد</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل العضو -->
<div class="modal fade" id="memberDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العضو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="member_details_content">
                <!-- تفاصيل العضو ستظهر هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allMembers = [];
let currentFundId = null;

// تحميل الأعضاء
function loadMembers() {
    const fundId = document.getElementById('fund_select').value;
    currentFundId = fundId;
    
    if (!fundId) {
        document.getElementById('members_tbody').innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    يرجى اختيار صندوق لعرض الأعضاء
                </td>
            </tr>
        `;
        return;
    }
    
    fetch(`/api/funds/${fundId}/members`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            allMembers = result.data;
            displayMembers(allMembers);
            loadFamilies(fundId);
        } else {
            showAlert('خطأ في تحميل الأعضاء: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل الأعضاء', 'danger');
    });
}

// عرض الأعضاء
function displayMembers(members) {
    const tbody = document.getElementById('members_tbody');
    
    if (members.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    لا يوجد أعضاء في هذا الصندوق
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    members.forEach(member => {
        const balanceClass = member.balance >= 0 ? 'text-success' : 'text-danger';
        
        html += `
            <tr>
                <td>
                    <strong>${member.name}</strong>
                    ${member.is_active ? '' : '<span class="badge bg-secondary ms-2">غير نشط</span>'}
                </td>
                <td>${member.phone || '-'}</td>
                <td>${member.email || '-'}</td>
                <td class="${balanceClass}">
                    <strong>${member.balance.toFixed(2)} ريال</strong>
                </td>
                <td>${member.family_name || '-'}</td>
                <td>${new Date(member.created_at).toLocaleDateString('ar-SA')}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-primary" onclick="viewMemberDetails(${member.id})" title="تفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="adjustBalance(${member.id})" title="تعديل الرصيد">
                            <i class="fas fa-coins"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editMember(${member.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteMember(${member.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// تصفية الأعضاء
function filterMembers() {
    const searchTerm = document.getElementById('search_members').value.toLowerCase();
    
    if (!searchTerm) {
        displayMembers(allMembers);
        return;
    }
    
    const filteredMembers = allMembers.filter(member => 
        member.name.toLowerCase().includes(searchTerm) ||
        (member.phone && member.phone.includes(searchTerm)) ||
        (member.email && member.email.toLowerCase().includes(searchTerm))
    );
    
    displayMembers(filteredMembers);
}

// إظهار مودال إضافة عضو
function showAddMemberModal() {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }
    
    document.getElementById('addMemberForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('addMemberModal'));
    modal.show();
}

// إضافة عضو جديد
function addMember() {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }
    
    const name = document.getElementById('member_name').value;
    const phone = document.getElementById('member_phone').value;
    const email = document.getElementById('member_email').value;
    const balance = parseFloat(document.getElementById('member_balance').value) || 0;
    const familyId = document.getElementById('member_family').value || null;
    const notes = document.getElementById('member_notes').value;
    
    if (!name) {
        showAlert('يرجى إدخال اسم العضو', 'warning');
        return;
    }
    
    const data = {
        name: name,
        phone: phone,
        email: email,
        balance: balance,
        family_id: familyId,
        notes: notes
    };
    
    fetch(`/api/funds/${currentFundId}/members`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('addMemberModal')).hide();
            showAlert('تم إضافة العضو بنجاح', 'success');
            loadMembers();
        } else {
            showAlert('خطأ في إضافة العضو: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إضافة العضو', 'danger');
    });
}

// إظهار مودال الاستيراد
function showImportModal() {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

// استيراد الأعضاء
function importMembers() {
    // هذه الوظيفة ستكون مبسطة في الوقت الحالي
    showAlert('ميزة الاستيراد قيد التطوير', 'info');
}

// تحميل العائلات
function loadFamilies(fundId) {
    fetch(`/api/funds/${fundId}/families`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const familySelect = document.getElementById('member_family');
            familySelect.innerHTML = '<option value="">بدون عائلة</option>';
            
            result.data.forEach(family => {
                familySelect.innerHTML += `<option value="${family.id}">${family.name}</option>`;
            });
        }
    })
    .catch(error => {
        console.error('Error loading families:', error);
    });
}

// عرض تفاصيل العضو
function viewMemberDetails(memberId) {
    fetch(`/api/members/${memberId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayMemberDetails(result.data);
            const modal = new bootstrap.Modal(document.getElementById('memberDetailsModal'));
            modal.show();
        } else {
            showAlert('خطأ في تحميل تفاصيل العضو', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل التفاصيل', 'danger');
    });
}

// عرض تفاصيل العضو في المودال
function displayMemberDetails(member) {
    const content = document.getElementById('member_details_content');
    const balanceClass = member.balance >= 0 ? 'text-success' : 'text-danger';
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات شخصية</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>الاسم:</strong></td>
                        <td>${member.name}</td>
                    </tr>
                    <tr>
                        <td><strong>الجوال:</strong></td>
                        <td>${member.phone || '-'}</td>
                    </tr>
                    <tr>
                        <td><strong>البريد:</strong></td>
                        <td>${member.email || '-'}</td>
                    </tr>
                    <tr>
                        <td><strong>العائلة:</strong></td>
                        <td>${member.family_name || '-'}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الانضمام:</strong></td>
                        <td>${new Date(member.created_at).toLocaleDateString('ar-SA')}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>المعلومات المالية</h6>
                <div class="card">
                    <div class="card-body text-center">
                        <h6>الرصيد الحالي</h6>
                        <h3 class="${balanceClass}">${member.balance.toFixed(2)} ريال</h3>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success btn-sm me-2" onclick="adjustBalance(${member.id})">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مبلغ
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="adjustBalance(${member.id})">
                        <i class="fas fa-minus me-1"></i>
                        خصم مبلغ
                    </button>
                </div>
            </div>
        </div>
        ${member.notes ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>ملاحظات</h6>
                <div class="alert alert-info">
                    ${member.notes}
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// تعديل رصيد العضو
function adjustBalance(memberId) {
    const amount = prompt('أدخل المبلغ (استخدم - للخصم):');
    if (amount === null || amount === '') return;
    
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) {
        showAlert('يرجى إدخال مبلغ صحيح', 'warning');
        return;
    }
    
    const data = {
        amount: numAmount,
        description: numAmount > 0 ? 'إضافة رصيد' : 'خصم رصيد'
    };
    
    fetch(`/api/members/${memberId}/adjust-balance`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('تم تعديل الرصيد بنجاح', 'success');
            loadMembers();
        } else {
            showAlert('خطأ في تعديل الرصيد: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تعديل الرصيد', 'danger');
    });
}

// تعديل العضو
function editMember(memberId) {
    showAlert('ميزة التعديل قيد التطوير', 'info');
}

// حذف العضو
function deleteMember(memberId) {
    if (!confirm('هل أنت متأكد من حذف هذا العضو؟')) {
        return;
    }
    
    fetch(`/api/members/${memberId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('تم حذف العضو بنجاح', 'success');
            loadMembers();
        } else {
            showAlert('خطأ في حذف العضو: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في حذف العضو', 'danger');
    });
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديد الصندوق من URL إذا كان موجوداً
    const urlParams = new URLSearchParams(window.location.search);
    const fundId = urlParams.get('fund_id');
    if (fundId) {
        document.getElementById('fund_select').value = fundId;
        loadMembers();
    }
});
</script>
{% endblock %}
