#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات لتطبيق صندوق التوفير
يحتوي على الاتصال وإنشاء الجداول والعمليات الأساسية
"""

import os
import sqlite3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager
from typing import Optional, Generator
import logging
from pathlib import Path

from .models import Base, User, Fund, Member, Family, Transaction, Note, Backup, Setting
from ..utils.security import hash_password

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = "data/tawfeer.db"):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        # التأكد من وجود مجلد البيانات
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        self.db_path = db_path
        self.engine = create_engine(
            f"sqlite:///{db_path}",
            echo=False,  # تعيين True لعرض استعلامات SQL
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        # إنشاء جلسة قاعدة البيانات
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # إنشاء الجداول
        self.create_tables()
        
        # إنشاء البيانات الافتراضية
        self.create_default_data()
    
    def create_tables(self):
        """إنشاء جميع الجداول"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
        except SQLAlchemyError as e:
            logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            raise
    
    def create_default_data(self):
        """إنشاء البيانات الافتراضية"""
        with self.get_session() as session:
            try:
                # التحقق من وجود مستخدم افتراضي
                admin_user = session.query(User).filter_by(username="admin").first()
                if not admin_user:
                    # إنشاء مستخدم مدير افتراضي
                    admin_user = User(
                        username="admin",
                        email="<EMAIL>",
                        password_hash=hash_password("admin123"),
                        full_name="مدير النظام",
                        role="admin"
                    )
                    session.add(admin_user)
                    session.commit()
                    logger.info("✅ تم إنشاء المستخدم الافتراضي")
                
                # إنشاء الإعدادات الافتراضية
                default_settings = [
                    ("app_name", "صندوق التوفير", "اسم التطبيق"),
                    ("app_version", "1.0.0", "إصدار التطبيق"),
                    ("theme", "light", "نمط المظهر"),
                    ("language", "ar", "لغة التطبيق"),
                    ("currency", "ريال", "العملة المستخدمة"),
                    ("backup_auto", "true", "النسخ الاحتياطي التلقائي"),
                    ("backup_interval", "7", "فترة النسخ الاحتياطي بالأيام")
                ]
                
                for key, value, description in default_settings:
                    setting = session.query(Setting).filter_by(key=key).first()
                    if not setting:
                        setting = Setting(key=key, value=value, description=description)
                        session.add(setting)
                
                session.commit()
                logger.info("✅ تم إنشاء الإعدادات الافتراضية")
                
            except SQLAlchemyError as e:
                session.rollback()
                logger.error(f"❌ خطأ في إنشاء البيانات الافتراضية: {e}")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """الحصول على جلسة قاعدة البيانات مع إدارة تلقائية للأخطاء"""
        session = self.SessionLocal()
        try:
            yield session
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"❌ خطأ في قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def get_db_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات (للاستخدام مع FastAPI)"""
        return self.SessionLocal()
    
    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        
        Args:
            backup_path: مسار النسخة الاحتياطية (اختياري)
            
        Returns:
            مسار ملف النسخة الاحتياطية
        """
        from datetime import datetime
        import shutil
        
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"data/backups/backup_{timestamp}.db"
        
        # التأكد من وجود مجلد النسخ الاحتياطية
        backup_dir = Path(backup_path).parent
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # نسخ ملف قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)
            
            # تسجيل النسخة الاحتياطية في قاعدة البيانات
            with self.get_session() as session:
                backup_record = Backup(
                    filename=Path(backup_path).name,
                    file_path=backup_path,
                    file_size=os.path.getsize(backup_path),
                    backup_type="manual"
                )
                session.add(backup_record)
                session.commit()
            
            logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise
    
    def restore_database(self, backup_path: str) -> bool:
        """
        استعادة قاعدة البيانات من نسخة احتياطية
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            
        Returns:
            True إذا تمت الاستعادة بنجاح
        """
        import shutil
        
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")
            
            # إنشاء نسخة احتياطية من الحالة الحالية
            current_backup = self.backup_database()
            logger.info(f"تم إنشاء نسخة احتياطية من الحالة الحالية: {current_backup}")
            
            # استعادة النسخة الاحتياطية
            shutil.copy2(backup_path, self.db_path)
            
            # إعادة تهيئة الاتصال
            self.engine.dispose()
            self.engine = create_engine(
                f"sqlite:///{self.db_path}",
                echo=False,
                pool_pre_ping=True,
                connect_args={"check_same_thread": False}
            )
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info(f"✅ تم استعادة قاعدة البيانات من: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة قاعدة البيانات: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """الحصول على معلومات قاعدة البيانات"""
        try:
            with self.get_session() as session:
                # عدد الجداول والسجلات
                tables_info = {}
                
                # عدد المستخدمين
                tables_info['users'] = session.query(User).count()
                
                # عدد الصناديق
                tables_info['funds'] = session.query(Fund).count()
                
                # عدد الأعضاء
                tables_info['members'] = session.query(Member).count()
                
                # عدد العائلات
                tables_info['families'] = session.query(Family).count()
                
                # عدد العمليات المالية
                tables_info['transactions'] = session.query(Transaction).count()
                
                # عدد الملاحظات
                tables_info['notes'] = session.query(Note).count()
                
                # حجم قاعدة البيانات
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                return {
                    'database_path': self.db_path,
                    'database_size': db_size,
                    'tables': tables_info,
                    'total_records': sum(tables_info.values())
                }
                
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معلومات قاعدة البيانات: {e}")
            return {}

# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()

# دالة للحصول على جلسة قاعدة البيانات (للاستخدام مع FastAPI)
def get_database():
    """دالة للحصول على جلسة قاعدة البيانات"""
    db = db_manager.get_db_session()
    try:
        yield db
    finally:
        db.close()
