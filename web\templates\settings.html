{% extends "base.html" %}

{% block title %}الإعدادات - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-cog me-2"></i>
        الإعدادات
    </h1>
</div>

<!-- إعدادات المظهر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-palette me-2"></i>
                    إعدادات المظهر
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المظهر</label>
                            <select class="form-control" id="theme_mode" onchange="changeTheme()">
                                <option value="light">فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">اللغة</label>
                            <select class="form-control" id="language">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نوع الأرقام</label>
                            <select class="form-control" id="number_format">
                                <option value="arabic">عربية (١٢٣)</option>
                                <option value="english">إنجليزية (123)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">العملة</label>
                            <select class="form-control" id="currency">
                                <option value="SAR">ريال سعودي</option>
                                <option value="USD">دولار أمريكي</option>
                                <option value="EUR">يورو</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="saveAppearanceSettings()">
                    <i class="fas fa-save me-2"></i>
                    حفظ إعدادات المظهر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- إدارة المستخدمين -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    إدارة المستخدمين
                </h5>
                <button class="btn btn-primary btn-sm" onclick="showAddUserModal()">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>الاسم الكامل</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="users_tbody">
                            <!-- المستخدمون سيظهرون هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- النسخ الاحتياطية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>
                    النسخ الاحتياطية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>إنشاء نسخة احتياطية</h6>
                        <p class="text-muted">احفظ نسخة من جميع بياناتك</p>
                        <button class="btn btn-success" onclick="createBackup()">
                            <i class="fas fa-download me-2"></i>
                            إنشاء نسخة احتياطية
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>استعادة نسخة احتياطية</h6>
                        <p class="text-muted">استعد البيانات من نسخة احتياطية</p>
                        <input type="file" class="form-control mb-2" id="backup_file" accept=".sql,.db">
                        <button class="btn btn-warning" onclick="restoreBackup()">
                            <i class="fas fa-upload me-2"></i>
                            استعادة النسخة
                        </button>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-12">
                        <h6>النسخ الاحتياطية التلقائية</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto_backup_enabled">
                            <label class="form-check-label" for="auto_backup_enabled">
                                تفعيل النسخ الاحتياطية التلقائية
                            </label>
                        </div>
                        <div class="mt-2">
                            <label class="form-label">تكرار النسخ الاحتياطية</label>
                            <select class="form-control" id="backup_frequency">
                                <option value="daily">يومياً</option>
                                <option value="weekly">أسبوعياً</option>
                                <option value="monthly">شهرياً</option>
                            </select>
                        </div>
                        <button class="btn btn-primary mt-2" onclick="saveBackupSettings()">
                            <i class="fas fa-save me-2"></i>
                            حفظ إعدادات النسخ الاحتياطية
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إعدادات النظام -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>
                    إعدادات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">اسم التطبيق</label>
                            <input type="text" class="form-control" id="app_name" value="صندوق التوفير">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">إصدار التطبيق</label>
                            <input type="text" class="form-control" id="app_version" value="1.0.0" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">مهلة انتهاء الجلسة (دقيقة)</label>
                            <input type="number" class="form-control" id="session_timeout" value="60">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">حد العمليات اليومية</label>
                            <input type="number" class="form-control" id="daily_transaction_limit" value="1000">
                        </div>
                    </div>
                </div>

                <button class="btn btn-primary" onclick="saveSystemSettings()">
                    <i class="fas fa-save me-2"></i>
                    حفظ إعدادات النظام
                </button>
            </div>
        </div>
    </div>
</div>

<!-- إعادة تعيين النظام -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    منطقة الخطر
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>إعادة تعيين البيانات</h6>
                        <p class="text-muted">حذف جميع البيانات والعودة للحالة الأولى</p>
                        <button class="btn btn-danger" onclick="resetSystem()">
                            <i class="fas fa-trash-alt me-2"></i>
                            إعادة تعيين النظام
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>تصدير جميع البيانات</h6>
                        <p class="text-muted">تصدير جميع البيانات قبل الحذف</p>
                        <button class="btn btn-warning" onclick="exportAllData()">
                            <i class="fas fa-file-export me-2"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة مستخدم -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="new_username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="new_full_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="new_email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الدور</label>
                        <select class="form-control" id="new_role" required>
                            <option value="member">عضو</option>
                            <option value="supervisor">مشرف</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">إضافة المستخدم</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل المستخدمين
function loadUsers() {
    fetch('/api/users')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayUsers(result.data);
        } else {
            showAlert('خطأ في تحميل المستخدمين: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل المستخدمين', 'danger');
    });
}

// عرض المستخدمين
function displayUsers(users) {
    const tbody = document.getElementById('users_tbody');

    let html = '';
    users.forEach(user => {
        const roleText = getRoleText(user.role);
        const statusBadge = user.is_active ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-danger">غير نشط</span>';

        html += `
            <tr>
                <td><strong>${user.username}</strong></td>
                <td>${user.full_name}</td>
                <td>${user.email}</td>
                <td><span class="badge bg-primary">${roleText}</span></td>
                <td>${statusBadge}</td>
                <td>${user.last_login ? new Date(user.last_login).toLocaleDateString('ar-SA') : '-'}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-warning" onclick="editUser(${user.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// الحصول على نص الدور
function getRoleText(role) {
    switch(role) {
        case 'admin': return 'مدير';
        case 'supervisor': return 'مشرف';
        case 'member': return 'عضو';
        default: return 'غير محدد';
    }
}

// تغيير المظهر
function changeTheme() {
    const theme = document.getElementById('theme_mode').value;
    // هذه الوظيفة ستكون مبسطة في الوقت الحالي
    showAlert(`تم تغيير المظهر إلى: ${theme}`, 'info');
}

// حفظ إعدادات المظهر
function saveAppearanceSettings() {
    const settings = {
        theme: document.getElementById('theme_mode').value,
        language: document.getElementById('language').value,
        number_format: document.getElementById('number_format').value,
        currency: document.getElementById('currency').value
    };

    // حفظ في localStorage مؤقتاً
    localStorage.setItem('appearance_settings', JSON.stringify(settings));
    showAlert('تم حفظ إعدادات المظهر بنجاح', 'success');
}

// إظهار مودال إضافة مستخدم
function showAddUserModal() {
    document.getElementById('addUserForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
    modal.show();
}

// إضافة مستخدم جديد
function addUser() {
    const username = document.getElementById('new_username').value;
    const fullName = document.getElementById('new_full_name').value;
    const email = document.getElementById('new_email').value;
    const password = document.getElementById('new_password').value;
    const role = document.getElementById('new_role').value;

    if (!username || !fullName || !email || !password) {
        showAlert('يرجى إدخال جميع البيانات المطلوبة', 'warning');
        return;
    }

    const data = {
        username: username,
        full_name: fullName,
        email: email,
        password: password,
        role: role
    };

    fetch('/api/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            showAlert('تم إضافة المستخدم بنجاح', 'success');
            loadUsers();
        } else {
            showAlert('خطأ في إضافة المستخدم: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إضافة المستخدم', 'danger');
    });
}

// تعديل مستخدم
function editUser(userId) {
    showAlert('ميزة تعديل المستخدم قيد التطوير', 'info');
}

// حذف مستخدم
function deleteUser(userId) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        return;
    }

    showAlert('ميزة حذف المستخدم قيد التطوير', 'info');
}

// إنشاء نسخة احتياطية
function createBackup() {
    fetch('/api/backup/create', {
        method: 'POST'
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.sql`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إنشاء النسخة الاحتياطية', 'danger');
    });
}

// استعادة نسخة احتياطية
function restoreBackup() {
    const fileInput = document.getElementById('backup_file');
    if (!fileInput.files[0]) {
        showAlert('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
        return;
    }

    if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية.')) {
        return;
    }

    showAlert('ميزة استعادة النسخة الاحتياطية قيد التطوير', 'info');
}

// حفظ إعدادات النسخ الاحتياطية
function saveBackupSettings() {
    const settings = {
        auto_backup_enabled: document.getElementById('auto_backup_enabled').checked,
        backup_frequency: document.getElementById('backup_frequency').value
    };

    localStorage.setItem('backup_settings', JSON.stringify(settings));
    showAlert('تم حفظ إعدادات النسخ الاحتياطية بنجاح', 'success');
}

// حفظ إعدادات النظام
function saveSystemSettings() {
    const settings = {
        app_name: document.getElementById('app_name').value,
        session_timeout: document.getElementById('session_timeout').value,
        daily_transaction_limit: document.getElementById('daily_transaction_limit').value
    };

    localStorage.setItem('system_settings', JSON.stringify(settings));
    showAlert('تم حفظ إعدادات النظام بنجاح', 'success');
}

// إعادة تعيين النظام
function resetSystem() {
    const confirmation = prompt('لتأكيد إعادة التعيين، اكتب "إعادة تعيين" في الحقل أدناه:');
    if (confirmation !== 'إعادة تعيين') {
        showAlert('تم إلغاء العملية', 'info');
        return;
    }

    if (!confirm('هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد؟')) {
        return;
    }

    showAlert('ميزة إعادة تعيين النظام قيد التطوير', 'warning');
}

// تصدير جميع البيانات
function exportAllData() {
    showAlert('ميزة تصدير البيانات قيد التطوير', 'info');
}

// تحميل الإعدادات المحفوظة
function loadSavedSettings() {
    // تحميل إعدادات المظهر
    const appearanceSettings = localStorage.getItem('appearance_settings');
    if (appearanceSettings) {
        const settings = JSON.parse(appearanceSettings);
        document.getElementById('theme_mode').value = settings.theme || 'light';
        document.getElementById('language').value = settings.language || 'ar';
        document.getElementById('number_format').value = settings.number_format || 'english';
        document.getElementById('currency').value = settings.currency || 'SAR';
    }

    // تحميل إعدادات النسخ الاحتياطية
    const backupSettings = localStorage.getItem('backup_settings');
    if (backupSettings) {
        const settings = JSON.parse(backupSettings);
        document.getElementById('auto_backup_enabled').checked = settings.auto_backup_enabled || false;
        document.getElementById('backup_frequency').value = settings.backup_frequency || 'weekly';
    }

    // تحميل إعدادات النظام
    const systemSettings = localStorage.getItem('system_settings');
    if (systemSettings) {
        const settings = JSON.parse(systemSettings);
        document.getElementById('app_name').value = settings.app_name || 'صندوق التوفير';
        document.getElementById('session_timeout').value = settings.session_timeout || 60;
        document.getElementById('daily_transaction_limit').value = settings.daily_transaction_limit || 1000;
    }
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحديث معلومات الصندوق النشط
function updateActiveFundInfo() {
    // الإعدادات لا تحتاج لصندوق نشط، لكن يمكن إظهار المعلومات للمرجع
    const activeFund = getActiveFund();
    if (activeFund.id && activeFund.name) {
        console.log('الصندوق النشط:', activeFund.name);
    }
}

// إعادة تحميل بيانات الصفحة عند تغيير الصندوق النشط
function reloadPageData() {
    updateActiveFundInfo();
}

// الاستماع لتغيير الصندوق النشط
window.addEventListener('activeFundChanged', function(event) {
    reloadPageData();
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSavedSettings();
    loadUsers();
    updateActiveFundInfo();
});
</script>
{% endblock %}