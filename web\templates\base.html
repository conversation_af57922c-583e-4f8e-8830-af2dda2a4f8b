<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}صندوق التوفير{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts (Arabic) - خطوط عربية محسنة -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{{ url_for('static', path='/css/style.css') }}" rel="stylesheet">

    <style>
        :root {
            --primary-font: 'Tajawal', 'Cairo', sans-serif;
            --heading-font: 'Cairo', 'Tajawal', sans-serif;
            --elegant-font: 'Amiri', serif;
        }

        body {
            font-family: var(--primary-font);
            background-color: #f8f9fa;
            direction: rtl;
            font-size: 16px;
            line-height: 1.6;
            font-weight: 400;
        }

        /* تحسين الخطوط للعناوين */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--heading-font);
            font-weight: 600;
            line-height: 1.4;
        }

        .navbar-brand {
            font-family: var(--heading-font);
            font-weight: 700;
            font-size: 1.6rem;
            letter-spacing: 0.5px;
        }

        /* تحسين خطوط القائمة الجانبية */
        .sidebar .nav-link {
            font-family: var(--primary-font);
            font-weight: 500;
            font-size: 15px;
            letter-spacing: 0.3px;
        }

        /* تحسين خطوط الأزرار */
        .btn {
            font-family: var(--primary-font);
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        /* تحسين خطوط الجداول */
        .table {
            font-family: var(--primary-font);
            font-size: 14px;
        }

        .table thead th {
            font-weight: 600;
            font-size: 15px;
            letter-spacing: 0.3px;
        }

        /* تحسين خطوط النماذج */
        .form-label {
            font-family: var(--primary-font);
            font-weight: 500;
            font-size: 14px;
        }

        .form-control, .form-select {
            font-family: var(--primary-font);
            font-size: 14px;
            font-weight: 400;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            min-height: 100vh;
            color: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 16px 24px;
            border-radius: 12px;
            margin: 4px 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: var(--primary-font);
            font-weight: 500;
            font-size: 15px;
            letter-spacing: 0.3px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
            transition: width 0.3s ease;
            z-index: 0;
        }

        .sidebar .nav-link:hover::before,
        .sidebar .nav-link.active::before {
            width: 100%;
        }

        .sidebar .nav-link i {
            position: relative;
            z-index: 1;
            width: 20px;
            text-align: center;
            margin-left: 12px;
            font-size: 16px;
        }

        .sidebar .nav-link span {
            position: relative;
            z-index: 1;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(155, 89, 182, 0.3));
            color: white;
            transform: translateX(-8px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .sidebar .nav-link:hover i,
        .sidebar .nav-link.active i {
            transform: scale(1.1);
            color: #3498db;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
        }
        
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stats-label {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .table thead th {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .alert {
            border: none;
            border-radius: 15px;
            padding: 20px;
            font-weight: 500;
        }
        
        .modal-content {
            border: none;
            border-radius: 20px;
        }
        
        .modal-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-radius: 20px 20px 0 0;
        }
        
        .footer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-top: 50px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-piggy-bank me-2"></i>
                صندوق التوفير
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user %}
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user %}
                    <!-- اختيار الصندوق النشط -->
                    <li class="nav-item me-3">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0 text-white">الصندوق النشط:</label>
                            <select class="form-select form-select-sm" id="active_fund_selector" onchange="changeActiveFund()" style="min-width: 200px;">
                                <option value="">اختر الصندوق</option>
                            </select>
                        </div>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/settings">الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/login">تسجيل الدخول</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% if user %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="position-sticky pt-3">
                    <!-- مؤشر الصندوق النشط -->
                    <div class="alert alert-info mb-3" id="active_fund_indicator" style="display: none;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-folder-open me-2"></i>
                            <div>
                                <small class="text-muted">الصندوق النشط:</small>
                                <div class="fw-bold" id="active_fund_name">لم يتم اختيار صندوق</div>
                            </div>
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/funds">
                                <i class="fas fa-folder-open"></i>
                                <span>إدارة الصناديق</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/members">
                                <i class="fas fa-users"></i>
                                <span>الأعضاء</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/families">
                                <i class="fas fa-home"></i>
                                <span>العائلات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/transactions">
                                <i class="fas fa-exchange-alt"></i>
                                <span>العمليات المالية</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/reports">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/services">
                                <i class="fas fa-tools"></i>
                                <span>الخدمات الإضافية</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i>
                                <span>الإعدادات</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10 main-content">
            {% else %}
            <div class="col-12">
            {% endif %}
                <main class="p-4">
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer text-center">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 صندوق التوفير. جميع الحقوق محفوظة.
                <span class="mx-2">|</span>
                تطوير: فريق التطوير
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', path='/js/app.js') }}"></script>

    <!-- Active Fund Management -->
    <script>
    // إدارة الصندوق النشط
    let activeFundId = null;
    let activeFundName = null;

    // تحميل الصناديق في القائمة المنسدلة
    function loadFundsSelector() {
        fetch('/api/funds')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                const selector = document.getElementById('active_fund_selector');
                selector.innerHTML = '<option value="">اختر الصندوق</option>';

                result.data.forEach(fund => {
                    const option = document.createElement('option');
                    option.value = fund.id;
                    option.textContent = `${fund.name} (${getFundTypeText(fund.fund_type)})`;
                    selector.appendChild(option);
                });

                // استعادة الصندوق النشط من localStorage
                const savedFundId = localStorage.getItem('active_fund_id');
                if (savedFundId && result.data.find(f => f.id == savedFundId)) {
                    selector.value = savedFundId;
                    changeActiveFund();
                }
            }
        })
        .catch(error => {
            console.error('Error loading funds:', error);
        });
    }

    // تغيير الصندوق النشط
    function changeActiveFund() {
        const selector = document.getElementById('active_fund_selector');
        const fundId = selector.value;

        if (fundId) {
            activeFundId = fundId;
            activeFundName = selector.options[selector.selectedIndex].text;

            // حفظ في localStorage
            localStorage.setItem('active_fund_id', fundId);
            localStorage.setItem('active_fund_name', activeFundName);

            // تحديث المؤشر
            updateActiveFundIndicator();

            // إشعار جميع الصفحات بتغيير الصندوق
            window.dispatchEvent(new CustomEvent('activeFundChanged', {
                detail: { fundId: fundId, fundName: activeFundName }
            }));

            // إعادة تحميل البيانات في الصفحة الحالية
            if (typeof reloadPageData === 'function') {
                reloadPageData();
            }

        } else {
            activeFundId = null;
            activeFundName = null;
            localStorage.removeItem('active_fund_id');
            localStorage.removeItem('active_fund_name');
            updateActiveFundIndicator();
        }
    }

    // تحديث مؤشر الصندوق النشط
    function updateActiveFundIndicator() {
        const indicator = document.getElementById('active_fund_indicator');
        const nameElement = document.getElementById('active_fund_name');

        if (activeFundId && activeFundName) {
            nameElement.textContent = activeFundName;
            indicator.style.display = 'block';
            indicator.className = 'alert alert-success mb-3';
        } else {
            nameElement.textContent = 'لم يتم اختيار صندوق';
            indicator.style.display = 'block';
            indicator.className = 'alert alert-warning mb-3';
        }
    }

    // الحصول على نص نوع الصندوق
    function getFundTypeText(type) {
        switch(type) {
            case 'family': return 'عائلي';
            case 'individual': return 'فردي';
            case 'personal': return 'شخصي';
            default: return 'غير محدد';
        }
    }

    // الحصول على الصندوق النشط
    function getActiveFund() {
        return {
            id: activeFundId,
            name: activeFundName
        };
    }

    // التحقق من وجود صندوق نشط
    function requireActiveFund() {
        if (!activeFundId) {
            alert('يرجى اختيار صندوق نشط أولاً من القائمة العلوية');
            return false;
        }
        return true;
    }

    // تهيئة النظام عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تحميل الصناديق إذا كان المستخدم مسجل دخول
        if (document.getElementById('active_fund_selector')) {
            loadFundsSelector();
        }

        // استعادة الصندوق النشط
        const savedFundId = localStorage.getItem('active_fund_id');
        const savedFundName = localStorage.getItem('active_fund_name');
        if (savedFundId && savedFundName) {
            activeFundId = savedFundId;
            activeFundName = savedFundName;
            updateActiveFundIndicator();
        } else {
            updateActiveFundIndicator();
        }
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
