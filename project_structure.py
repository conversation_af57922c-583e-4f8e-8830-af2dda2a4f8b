#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت إنشاء هيكل مشروع صندوق التوفير
يقوم بإنشاء جميع المجلدات والملفات الأساسية للمشروع
"""

import os
import sys
from pathlib import Path

def create_project_structure():
    """إنشاء هيكل المشروع الكامل"""
    
    # المجلدات الرئيسية
    directories = [
        "src",
        "src/core",
        "src/database",
        "src/models",
        "src/ui",
        "src/ui/screens",
        "src/ui/components",
        "src/ui/assets",
        "src/ui/assets/images",
        "src/ui/assets/fonts",
        "src/services",
        "src/utils",
        "src/reports",
        "src/auth",
        "web",
        "web/static",
        "web/static/css",
        "web/static/js",
        "web/static/images",
        "web/templates",
        "scripts",
        "scripts/build",
        "scripts/deploy",
        "data",
        "data/backups",
        "data/exports",
        "tests",
        "tests/unit",
        "tests/integration",
        "docs",
        "config",
        "logs"
    ]
    
    # إنشاء المجلدات
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء المجلد: {directory}")
    
    # إنشاء ملفات __init__.py
    init_files = [
        "src/__init__.py",
        "src/core/__init__.py",
        "src/database/__init__.py",
        "src/models/__init__.py",
        "src/ui/__init__.py",
        "src/ui/screens/__init__.py",
        "src/ui/components/__init__.py",
        "src/services/__init__.py",
        "src/utils/__init__.py",
        "src/reports/__init__.py",
        "src/auth/__init__.py",
        "tests/__init__.py",
        "tests/unit/__init__.py",
        "tests/integration/__init__.py"
    ]
    
    for init_file in init_files:
        Path(init_file).touch()
        print(f"✅ تم إنشاء الملف: {init_file}")
    
    print("\n🎉 تم إنشاء هيكل المشروع بنجاح!")
    print("📁 المجلدات المُنشأة:")
    for directory in directories:
        print(f"   - {directory}")

if __name__ == "__main__":
    create_project_structure()
