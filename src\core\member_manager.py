#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأعضاء - إدارة جميع عمليات الأعضاء
يحتوي على جميع الوظائف المتعلقة بإدارة أعضاء الصناديق
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime
import logging
import csv
import io

from ..database.models import Member, Fund, Family, Transaction, TransactionType
from ..database.database import db_manager

logger = logging.getLogger(__name__)

class MemberManager:
    """مدير الأعضاء"""
    
    def __init__(self):
        """تهيئة مدير الأعضاء"""
        pass
    
    def add_member(
        self,
        fund_id: int,
        name: str,
        phone: Optional[str] = None,
        email: Optional[str] = None,
        national_id: Optional[str] = None,
        address: Optional[str] = None,
        family_id: Optional[int] = None,
        initial_balance: float = 0.0,
        notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        إضافة عضو جديد
        
        Args:
            fund_id: معرف الصندوق
            name: اسم العضو
            phone: رقم الهاتف
            email: البريد الإلكتروني
            national_id: رقم الهوية
            address: العنوان
            family_id: معرف العائلة (اختياري)
            initial_balance: الرصيد الأولي
            notes: ملاحظات
            
        Returns:
            معلومات العضو المُضاف
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من وجود الصندوق
                fund = session.query(Fund).filter_by(id=fund_id, is_active=True).first()
                if not fund:
                    return {"success": False, "message": "الصندوق غير موجود"}
                
                # التحقق من وجود العائلة إذا تم تحديدها
                if family_id:
                    family = session.query(Family).filter_by(id=family_id, fund_id=fund_id).first()
                    if not family:
                        return {"success": False, "message": "العائلة غير موجودة"}
                
                # التحقق من عدم تكرار الاسم في نفس الصندوق
                existing_member = session.query(Member).filter(
                    and_(Member.name == name, Member.fund_id == fund_id, Member.is_active == True)
                ).first()
                
                if existing_member:
                    return {"success": False, "message": "يوجد عضو بنفس الاسم في الصندوق"}
                
                # إنشاء العضو الجديد
                new_member = Member(
                    name=name,
                    phone=phone,
                    email=email,
                    national_id=national_id,
                    address=address,
                    balance=initial_balance,
                    fund_id=fund_id,
                    family_id=family_id,
                    notes=notes
                )
                
                session.add(new_member)
                session.commit()
                session.refresh(new_member)
                
                # تحديث رصيد الصندوق
                fund.total_balance += initial_balance
                
                # تحديث عدد أفراد العائلة إذا كان العضو ينتمي لعائلة
                if family_id:
                    family = session.query(Family).filter_by(id=family_id).first()
                    if family:
                        family.total_members += 1
                        family.total_balance += initial_balance
                
                session.commit()
                
                logger.info(f"تم إضافة عضو جديد: {name} للصندوق {fund.name}")
                
                return {
                    "success": True,
                    "message": "تم إضافة العضو بنجاح",
                    "member": {
                        "id": new_member.id,
                        "name": new_member.name,
                        "phone": new_member.phone,
                        "email": new_member.email,
                        "balance": new_member.balance,
                        "family_id": new_member.family_id,
                        "created_at": new_member.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"خطأ في إضافة العضو: {e}")
            return {"success": False, "message": f"خطأ في إضافة العضو: {str(e)}"}
    
    def get_fund_members(self, fund_id: int, family_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        الحصول على أعضاء الصندوق
        
        Args:
            fund_id: معرف الصندوق
            family_id: معرف العائلة (اختياري للتصفية)
            
        Returns:
            قائمة بأعضاء الصندوق
        """
        try:
            with db_manager.get_session() as session:
                query = session.query(Member).filter(
                    and_(Member.fund_id == fund_id, Member.is_active == True)
                )
                
                if family_id:
                    query = query.filter(Member.family_id == family_id)
                
                members = query.all()
                
                members_list = []
                for member in members:
                    # الحصول على اسم العائلة
                    family_name = None
                    if member.family_id:
                        family = session.query(Family).filter_by(id=member.family_id).first()
                        if family:
                            family_name = family.name
                    
                    # حساب عدد العمليات
                    transactions_count = session.query(Transaction).filter_by(member_id=member.id).count()
                    
                    members_list.append({
                        "id": member.id,
                        "name": member.name,
                        "phone": member.phone,
                        "email": member.email,
                        "national_id": member.national_id,
                        "address": member.address,
                        "balance": member.balance,
                        "family_id": member.family_id,
                        "family_name": family_name,
                        "transactions_count": transactions_count,
                        "notes": member.notes,
                        "created_at": member.created_at.isoformat(),
                        "updated_at": member.updated_at.isoformat()
                    })
                
                return members_list
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على أعضاء الصندوق: {e}")
            return []
    
    def get_member_details(self, member_id: int, fund_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل عضو معين
        
        Args:
            member_id: معرف العضو
            fund_id: معرف الصندوق
            
        Returns:
            تفاصيل العضو أو None
        """
        try:
            with db_manager.get_session() as session:
                member = session.query(Member).filter(
                    and_(Member.id == member_id, Member.fund_id == fund_id, Member.is_active == True)
                ).first()
                
                if not member:
                    return None
                
                # الحصول على اسم العائلة
                family_name = None
                if member.family_id:
                    family = session.query(Family).filter_by(id=member.family_id).first()
                    if family:
                        family_name = family.name
                
                # حساب الإحصائيات
                transactions = session.query(Transaction).filter_by(member_id=member.id).all()
                
                total_income = sum(t.amount for t in transactions if t.transaction_type == TransactionType.INCOME)
                total_expense = sum(t.amount for t in transactions if t.transaction_type == TransactionType.EXPENSE)
                total_withdrawals = sum(t.amount for t in transactions if t.transaction_type == TransactionType.WITHDRAWAL)
                total_loans = sum(t.amount for t in transactions if t.transaction_type == TransactionType.LOAN)
                
                return {
                    "id": member.id,
                    "name": member.name,
                    "phone": member.phone,
                    "email": member.email,
                    "national_id": member.national_id,
                    "address": member.address,
                    "balance": member.balance,
                    "family_id": member.family_id,
                    "family_name": family_name,
                    "notes": member.notes,
                    "statistics": {
                        "total_transactions": len(transactions),
                        "total_income": float(total_income),
                        "total_expense": float(total_expense),
                        "total_withdrawals": float(total_withdrawals),
                        "total_loans": float(total_loans)
                    },
                    "created_at": member.created_at.isoformat(),
                    "updated_at": member.updated_at.isoformat()
                }
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل العضو: {e}")
            return None
    
    def update_member(
        self,
        member_id: int,
        fund_id: int,
        name: Optional[str] = None,
        phone: Optional[str] = None,
        email: Optional[str] = None,
        national_id: Optional[str] = None,
        address: Optional[str] = None,
        family_id: Optional[int] = None,
        notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        تحديث معلومات العضو
        
        Args:
            member_id: معرف العضو
            fund_id: معرف الصندوق
            name: الاسم الجديد
            phone: رقم الهاتف الجديد
            email: البريد الإلكتروني الجديد
            national_id: رقم الهوية الجديد
            address: العنوان الجديد
            family_id: معرف العائلة الجديد
            notes: الملاحظات الجديدة
            
        Returns:
            نتيجة التحديث
        """
        try:
            with db_manager.get_session() as session:
                member = session.query(Member).filter(
                    and_(Member.id == member_id, Member.fund_id == fund_id, Member.is_active == True)
                ).first()
                
                if not member:
                    return {"success": False, "message": "العضو غير موجود"}
                
                old_family_id = member.family_id
                
                # تحديث الحقول المطلوبة
                if name:
                    # التحقق من عدم تكرار الاسم
                    existing_member = session.query(Member).filter(
                        and_(
                            Member.name == name,
                            Member.fund_id == fund_id,
                            Member.id != member_id,
                            Member.is_active == True
                        )
                    ).first()
                    
                    if existing_member:
                        return {"success": False, "message": "يوجد عضو آخر بنفس الاسم"}
                    
                    member.name = name
                
                if phone is not None:
                    member.phone = phone
                
                if email is not None:
                    member.email = email
                
                if national_id is not None:
                    member.national_id = national_id
                
                if address is not None:
                    member.address = address
                
                if notes is not None:
                    member.notes = notes
                
                # تحديث العائلة
                if family_id != old_family_id:
                    # إزالة من العائلة القديمة
                    if old_family_id:
                        old_family = session.query(Family).filter_by(id=old_family_id).first()
                        if old_family:
                            old_family.total_members -= 1
                            old_family.total_balance -= member.balance
                    
                    # إضافة للعائلة الجديدة
                    if family_id:
                        new_family = session.query(Family).filter_by(id=family_id, fund_id=fund_id).first()
                        if not new_family:
                            return {"success": False, "message": "العائلة الجديدة غير موجودة"}
                        
                        new_family.total_members += 1
                        new_family.total_balance += member.balance
                    
                    member.family_id = family_id
                
                member.updated_at = datetime.utcnow()
                session.commit()
                
                logger.info(f"تم تحديث العضو: {member.name}")
                
                return {"success": True, "message": "تم تحديث العضو بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في تحديث العضو: {e}")
            return {"success": False, "message": f"خطأ في تحديث العضو: {str(e)}"}

    def delete_member(self, member_id: int) -> Dict[str, Any]:
        """
        حذف عضو

        Args:
            member_id: معرف العضو

        Returns:
            نتيجة الحذف
        """
        try:
            with db_manager.get_session() as session:
                member = session.query(Member).filter(
                    and_(Member.id == member_id, Member.is_active == True)
                ).first()

                if not member:
                    return {"success": False, "message": "العضو غير موجود"}

                # تحديث العائلة إذا كان العضو ينتمي لعائلة
                if member.family_id:
                    family = session.query(Family).filter_by(id=member.family_id).first()
                    if family:
                        family.total_members -= 1
                        family.total_balance -= member.balance

                # وضع علامة الحذف بدلاً من الحذف الفعلي
                member.is_active = False
                member.updated_at = datetime.utcnow()

                session.commit()

                logger.info(f"تم حذف العضو: {member.name}")

                return {"success": True, "message": "تم حذف العضو بنجاح"}

        except Exception as e:
            logger.error(f"خطأ في حذف العضو: {e}")
            return {"success": False, "message": f"خطأ في حذف العضو: {str(e)}"}

    def adjust_member_balance(self, member_id: int, operation: str, amount: float,
                            description: str = "تعديل رصيد") -> Dict[str, Any]:
        """
        تعديل رصيد العضو

        Args:
            member_id: معرف العضو
            operation: نوع العملية (add, subtract, set)
            amount: المبلغ
            description: وصف العملية

        Returns:
            نتيجة التعديل
        """
        try:
            with db_manager.get_session() as session:
                member = session.query(Member).filter(
                    and_(Member.id == member_id, Member.is_active == True)
                ).first()

                if not member:
                    return {"success": False, "message": "العضو غير موجود"}

                old_balance = member.balance

                # حساب الرصيد الجديد
                if operation == 'add':
                    new_balance = old_balance + amount
                elif operation == 'subtract':
                    new_balance = old_balance - amount
                elif operation == 'set':
                    new_balance = amount
                else:
                    new_balance = old_balance + amount  # افتراضي

                # تحديث الرصيد
                member.balance = new_balance
                member.updated_at = datetime.utcnow()

                # تحديث رصيد العائلة إذا كان العضو ينتمي لعائلة
                if member.family_id:
                    family = session.query(Family).filter_by(id=member.family_id).first()
                    if family:
                        family.total_balance = family.total_balance - old_balance + new_balance

                # إضافة سجل في المعاملات
                from ..database.models import Transaction, TransactionType

                transaction = Transaction(
                    fund_id=member.fund_id,
                    member_id=member_id,
                    transaction_type=TransactionType.ADJUSTMENT,
                    amount=amount,
                    description=description,
                    created_by=1,  # سيتم تحديثه لاحقاً
                    created_at=datetime.utcnow()
                )

                session.add(transaction)
                session.commit()

                logger.info(f"تم تعديل رصيد العضو {member.name}: {old_balance} -> {new_balance}")

                return {
                    "success": True,
                    "message": "تم تعديل الرصيد بنجاح",
                    "old_balance": float(old_balance),
                    "new_balance": float(new_balance)
                }

        except Exception as e:
            logger.error(f"خطأ في تعديل الرصيد: {e}")
            return {"success": False, "message": f"خطأ في تعديل الرصيد: {str(e)}"}

# إنشاء مثيل مدير الأعضاء
member_manager = MemberManager()
