/* ملف الأنماط الرئيسي لتطبيق صندوق التوفير */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات العامة */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --font-family: 'Cairo', sans-serif;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    direction: rtl;
    text-align: right;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* تحسين الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #1e7e34);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #e0a800);
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #c82333);
}

.btn-info {
    background: linear-gradient(45deg, var(--info-color), #138496);
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* تحسين النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: scale(1.02);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
    transition: var(--transition);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin: 5px 10px;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transition: var(--transition);
    transform: scale(0);
}

.stats-card:hover::before {
    transform: scale(1);
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.stats-label {
    font-size: 1.2rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* الجداول */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 15px;
    padding: 20px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

/* النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: 20px;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border-bottom: none;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    border-top: none;
    padding: 20px 30px;
}

/* التذييل */
.footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-top: 50px;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* تطبيق الرسوم المتحركة */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        border-radius: 15px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .stats-label {
        font-size: 1rem;
    }
}

/* تحسينات إضافية */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.badge {
    border-radius: 20px;
    padding: 5px 10px;
    font-weight: 600;
}

.progress {
    height: 10px;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين الأيقونات */
.icon-lg {
    font-size: 2rem;
}

.icon-xl {
    font-size: 3rem;
}

/* تأثيرات التمرير */
.scroll-smooth {
    scroll-behavior: smooth;
}

/* تحسين التركيز */
.form-control:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* تحسين الطباعة */
@media print {
    .sidebar,
    .navbar,
    .footer,
    .btn {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
