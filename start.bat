@echo off
chcp 65001 >nul
title صندوق التوفير - Tawfeer Savings Fund

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                    🏦 صندوق التوفير 🏦                      ║
echo ║                                                              ║
echo ║              تطبيق شامل لإدارة الصناديق والأعضاء              ║
echo ║                                                              ║
echo ║                        الإصدار 1.0.0                        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo ══════════════════════════════════════════════════════════════
echo 🎯 اختر العملية المطلوبة:
echo ══════════════════════════════════════════════════════════════
echo 1. 🖥️  تشغيل تطبيق سطح المكتب
echo 2. 🌐 تشغيل تطبيق الويب
echo 3. 📦 تثبيت المتطلبات
echo 4. 🧪 تشغيل الاختبارات
echo 5. 🔨 بناء المشروع
echo 6. 🧹 تنظيف المشروع
echo 7. ℹ️  عرض معلومات المشروع
echo 8. ⚙️  إعداد المشروع
echo 0. 🚪 خروج
echo ══════════════════════════════════════════════════════════════

set /p choice="👆 اختر رقم العملية: "

if "%choice%"=="1" goto DESKTOP
if "%choice%"=="2" goto WEB
if "%choice%"=="3" goto INSTALL
if "%choice%"=="4" goto TEST
if "%choice%"=="5" goto BUILD
if "%choice%"=="6" goto CLEAN
if "%choice%"=="7" goto INFO
if "%choice%"=="8" goto SETUP
if "%choice%"=="0" goto EXIT

echo ❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى
pause
goto MENU

:DESKTOP
echo 🖥️ تشغيل تطبيق سطح المكتب...
python run.py --desktop
pause
goto MENU

:WEB
echo 🌐 تشغيل تطبيق الويب...
echo 📍 سيتم فتح المتصفح على: http://localhost:8000
python run.py --web
pause
goto MENU

:INSTALL
echo 📦 تثبيت المتطلبات...
python run.py --install
pause
goto MENU

:TEST
echo 🧪 تشغيل الاختبارات...
python run.py --test
pause
goto MENU

:BUILD
echo 🔨 بناء المشروع...
python run.py --build
pause
goto MENU

:CLEAN
echo 🧹 تنظيف المشروع...
python run.py --clean
pause
goto MENU

:INFO
echo ℹ️ عرض معلومات المشروع...
python run.py --info
pause
goto MENU

:SETUP
echo ⚙️ إعداد المشروع...
python run.py --setup
pause
goto MENU

:EXIT
echo 👋 شكراً لاستخدام صندوق التوفير!
pause
exit
