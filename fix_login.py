#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكربت إصلاح مشكلة تسجيل الدخول
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_and_fix_login():
    """فحص وإصلاح مشكلة تسجيل الدخول"""
    
    print("🔍 فحص قاعدة البيانات...")
    
    try:
        from src.database.database import db_manager
        from src.database.models import User, UserRole
        from src.utils.security import hash_password, verify_password
        from src.auth.auth_manager import auth_manager
        
        # فحص قاعدة البيانات
        info = db_manager.get_database_info()
        print(f"📊 عدد المستخدمين في قاعدة البيانات: {info.get('tables', {}).get('users', 0)}")
        
        # فحص المستخدم الافتراضي
        with db_manager.get_session() as session:
            admin_user = session.query(User).filter_by(username="admin").first()
            
            if admin_user:
                print("✅ المستخدم الافتراضي موجود")
                print(f"   - اسم المستخدم: {admin_user.username}")
                print(f"   - البريد الإلكتروني: {admin_user.email}")
                print(f"   - الاسم الكامل: {admin_user.full_name}")
                print(f"   - الدور: {admin_user.role}")
                print(f"   - نشط: {admin_user.is_active}")
                
                # اختبار كلمة المرور
                password_test = verify_password("admin123", admin_user.password_hash)
                print(f"   - اختبار كلمة المرور: {'✅ صحيحة' if password_test else '❌ خاطئة'}")
                
                if not password_test:
                    print("🔧 إعادة تعيين كلمة المرور...")
                    admin_user.password_hash = hash_password("admin123")
                    session.commit()
                    print("✅ تم إعادة تعيين كلمة المرور")
                
            else:
                print("❌ المستخدم الافتراضي غير موجود - إنشاء مستخدم جديد...")
                
                # إنشاء مستخدم افتراضي جديد
                new_admin = User(
                    username="admin",
                    email="<EMAIL>",
                    password_hash=hash_password("admin123"),
                    full_name="مدير النظام",
                    role=UserRole.ADMIN,
                    is_active=True
                )
                
                session.add(new_admin)
                session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي")
        
        # اختبار تسجيل الدخول
        print("\n🧪 اختبار تسجيل الدخول...")
        result = auth_manager.login("admin", "admin123")
        
        if result["success"]:
            print("✅ تسجيل الدخول نجح!")
            print(f"   - الرسالة: {result['message']}")
            print(f"   - المستخدم: {result['user']['full_name']}")
        else:
            print("❌ تسجيل الدخول فشل!")
            print(f"   - الرسالة: {result['message']}")
            
            # محاولة إصلاح إضافية
            print("🔧 محاولة إصلاح إضافية...")
            
            with db_manager.get_session() as session:
                # حذف المستخدم القديم إذا كان موجوداً
                old_admin = session.query(User).filter_by(username="admin").first()
                if old_admin:
                    session.delete(old_admin)
                
                # إنشاء مستخدم جديد
                fresh_admin = User(
                    username="admin",
                    email="<EMAIL>",
                    password_hash=hash_password("admin123"),
                    full_name="مدير النظام",
                    role=UserRole.ADMIN,
                    is_active=True
                )
                
                session.add(fresh_admin)
                session.commit()
                print("✅ تم إنشاء مستخدم جديد")
                
                # اختبار مرة أخرى
                result2 = auth_manager.login("admin", "admin123")
                if result2["success"]:
                    print("✅ تسجيل الدخول نجح بعد الإصلاح!")
                else:
                    print("❌ لا يزال هناك مشكلة:")
                    print(f"   - الرسالة: {result2['message']}")
        
        print("\n" + "="*50)
        print("🎯 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("="*50)
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_and_fix_login()
