#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الصناديق - إدارة جميع عمليات الصناديق
يحتوي على جميع الوظائف المتعلقة بإنشاء وإدارة الصناديق
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
import logging

from ..database.models import Fund, User, Member, Family, Transaction, FundType
from ..utils.security import hash_password, verify_password
from ..database.database import db_manager

logger = logging.getLogger(__name__)

class FundManager:
    """مدير الصناديق"""
    
    def __init__(self):
        """تهيئة مدير الصناديق"""
        pass
    
    def create_fund(
        self,
        name: str,
        description: str,
        fund_type: str,
        owner_id: int,
        password: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        إنشاء صندوق جديد
        
        Args:
            name: اسم الصندوق
            description: وصف الصندوق
            fund_type: نوع الصندوق (family, individual, personal)
            owner_id: معرف المالك
            password: كلمة مرور الصندوق (اختيارية)
            
        Returns:
            معلومات الصندوق المُنشأ
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من وجود المستخدم
                user = session.query(User).filter_by(id=owner_id).first()
                if not user:
                    return {"success": False, "message": "المستخدم غير موجود"}
                
                # التحقق من عدم تكرار اسم الصندوق للمستخدم نفسه
                existing_fund = session.query(Fund).filter(
                    and_(Fund.name == name, Fund.owner_id == owner_id)
                ).first()
                
                if existing_fund:
                    return {"success": False, "message": "يوجد صندوق بنفس الاسم"}
                
                # تشفير كلمة المرور إذا تم توفيرها
                password_hash = None
                if password:
                    password_hash = hash_password(password)
                
                # إنشاء الصندوق
                new_fund = Fund(
                    name=name,
                    description=description,
                    fund_type=FundType(fund_type),
                    owner_id=owner_id,
                    password_hash=password_hash
                )
                
                session.add(new_fund)
                session.commit()
                session.refresh(new_fund)
                
                logger.info(f"تم إنشاء صندوق جديد: {name} للمستخدم {user.username}")
                
                return {
                    "success": True,
                    "message": "تم إنشاء الصندوق بنجاح",
                    "fund": {
                        "id": new_fund.id,
                        "name": new_fund.name,
                        "description": new_fund.description,
                        "fund_type": new_fund.fund_type.value,
                        "total_balance": new_fund.total_balance,
                        "created_at": new_fund.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء الصندوق: {e}")
            return {"success": False, "message": f"خطأ في إنشاء الصندوق: {str(e)}"}
    
    def get_user_funds(self, user_id: int) -> List[Dict[str, Any]]:
        """
        الحصول على جميع صناديق المستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            قائمة بصناديق المستخدم
        """
        try:
            with db_manager.get_session() as session:
                funds = session.query(Fund).filter(
                    and_(Fund.owner_id == user_id, Fund.is_active == True)
                ).all()
                
                funds_list = []
                for fund in funds:
                    # حساب عدد الأعضاء
                    members_count = session.query(Member).filter(
                        and_(Member.fund_id == fund.id, Member.is_active == True)
                    ).count()
                    
                    # حساب عدد العائلات
                    families_count = session.query(Family).filter_by(fund_id=fund.id).count()
                    
                    funds_list.append({
                        "id": fund.id,
                        "name": fund.name,
                        "description": fund.description,
                        "fund_type": fund.fund_type.value,
                        "total_balance": fund.total_balance,
                        "members_count": members_count,
                        "families_count": families_count,
                        "has_password": fund.password_hash is not None,
                        "created_at": fund.created_at.isoformat(),
                        "updated_at": fund.updated_at.isoformat()
                    })
                
                return funds_list
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على صناديق المستخدم: {e}")
            return []
    
    def get_fund_details(self, fund_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل صندوق معين
        
        Args:
            fund_id: معرف الصندوق
            user_id: معرف المستخدم
            
        Returns:
            تفاصيل الصندوق أو None
        """
        try:
            with db_manager.get_session() as session:
                fund = session.query(Fund).filter(
                    and_(Fund.id == fund_id, Fund.owner_id == user_id, Fund.is_active == True)
                ).first()
                
                if not fund:
                    return None
                
                # حساب الإحصائيات
                members_count = session.query(Member).filter(
                    and_(Member.fund_id == fund.id, Member.is_active == True)
                ).count()
                
                families_count = session.query(Family).filter_by(fund_id=fund.id).count()
                
                transactions_count = session.query(Transaction).filter_by(fund_id=fund.id).count()
                
                # حساب إجمالي الإيرادات والمصروفات
                from sqlalchemy import func
                from ..database.models import TransactionType

                income_total = session.query(func.sum(Transaction.amount)).filter(
                    and_(
                        Transaction.fund_id == fund.id,
                        Transaction.transaction_type == TransactionType.INCOME
                    )
                ).scalar() or 0

                expense_total = session.query(func.sum(Transaction.amount)).filter(
                    and_(
                        Transaction.fund_id == fund.id,
                        Transaction.transaction_type == TransactionType.EXPENSE
                    )
                ).scalar() or 0
                
                return {
                    "id": fund.id,
                    "name": fund.name,
                    "description": fund.description,
                    "fund_type": fund.fund_type.value,
                    "total_balance": fund.total_balance,
                    "has_password": fund.password_hash is not None,
                    "statistics": {
                        "members_count": members_count,
                        "families_count": families_count,
                        "transactions_count": transactions_count,
                        "total_income": float(income_total),
                        "total_expense": float(expense_total)
                    },
                    "created_at": fund.created_at.isoformat(),
                    "updated_at": fund.updated_at.isoformat()
                }
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل الصندوق: {e}")
            return None
    
    def update_fund(
        self,
        fund_id: int,
        user_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        password: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        تحديث معلومات الصندوق
        
        Args:
            fund_id: معرف الصندوق
            user_id: معرف المستخدم
            name: الاسم الجديد (اختياري)
            description: الوصف الجديد (اختياري)
            password: كلمة المرور الجديدة (اختياري)
            
        Returns:
            نتيجة التحديث
        """
        try:
            with db_manager.get_session() as session:
                fund = session.query(Fund).filter(
                    and_(Fund.id == fund_id, Fund.owner_id == user_id, Fund.is_active == True)
                ).first()
                
                if not fund:
                    return {"success": False, "message": "الصندوق غير موجود"}
                
                # تحديث الحقول المطلوبة
                if name:
                    # التحقق من عدم تكرار الاسم
                    existing_fund = session.query(Fund).filter(
                        and_(
                            Fund.name == name,
                            Fund.owner_id == user_id,
                            Fund.id != fund_id
                        )
                    ).first()
                    
                    if existing_fund:
                        return {"success": False, "message": "يوجد صندوق آخر بنفس الاسم"}
                    
                    fund.name = name
                
                if description is not None:
                    fund.description = description
                
                if password is not None:
                    if password:
                        fund.password_hash = hash_password(password)
                    else:
                        fund.password_hash = None
                
                fund.updated_at = datetime.utcnow()
                session.commit()
                
                logger.info(f"تم تحديث الصندوق: {fund.name}")
                
                return {"success": True, "message": "تم تحديث الصندوق بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في تحديث الصندوق: {e}")
            return {"success": False, "message": f"خطأ في تحديث الصندوق: {str(e)}"}
    
    def verify_fund_password(self, fund_id: int, password: str) -> bool:
        """
        التحقق من كلمة مرور الصندوق
        
        Args:
            fund_id: معرف الصندوق
            password: كلمة المرور
            
        Returns:
            True إذا كانت كلمة المرور صحيحة
        """
        try:
            with db_manager.get_session() as session:
                fund = session.query(Fund).filter_by(id=fund_id).first()
                
                if not fund or not fund.password_hash:
                    return True  # لا توجد كلمة مرور
                
                return verify_password(password, fund.password_hash)
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من كلمة مرور الصندوق: {e}")
            return False
    
    def delete_fund(self, fund_id: int, user_id: int) -> Dict[str, Any]:
        """
        حذف صندوق (إلغاء تفعيل)
        
        Args:
            fund_id: معرف الصندوق
            user_id: معرف المستخدم
            
        Returns:
            نتيجة الحذف
        """
        try:
            with db_manager.get_session() as session:
                fund = session.query(Fund).filter(
                    and_(Fund.id == fund_id, Fund.owner_id == user_id)
                ).first()
                
                if not fund:
                    return {"success": False, "message": "الصندوق غير موجود"}
                
                # إلغاء تفعيل الصندوق بدلاً من حذفه
                fund.is_active = False
                fund.updated_at = datetime.utcnow()
                session.commit()
                
                logger.info(f"تم حذف الصندوق: {fund.name}")
                
                return {"success": True, "message": "تم حذف الصندوق بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في حذف الصندوق: {e}")
            return {"success": False, "message": f"خطأ في حذف الصندوق: {str(e)}"}

# إنشاء مثيل مدير الصناديق
fund_manager = FundManager()
