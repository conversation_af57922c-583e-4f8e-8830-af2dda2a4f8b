#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات أساسية لتطبيق صندوق التوفير
تتضمن اختبارات الوحدة الأساسية للتأكد من عمل النظام
"""

import pytest
import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.auth.auth_manager import auth_manager
from src.core.fund_manager import fund_manager
from src.core.member_manager import member_manager
from src.core.family_manager import family_manager
from src.core.transaction_manager import transaction_manager
from src.utils.security import hash_password, verify_password
from src.database.database import db_manager

class TestSecurity:
    """اختبارات الأمان والتشفير"""
    
    def test_password_hashing(self):
        """اختبار تشفير كلمات المرور"""
        password = "test_password_123"
        hashed = hash_password(password)
        
        # التأكد من أن كلمة المرور تم تشفيرها
        assert hashed != password
        assert len(hashed) > 0
        
        # التأكد من صحة التحقق
        assert verify_password(password, hashed) == True
        assert verify_password("wrong_password", hashed) == False
    
    def test_password_strength_validation(self):
        """اختبار التحقق من قوة كلمة المرور"""
        from src.utils.security import validate_password_strength
        
        # كلمة مرور قوية
        strong_password = "StrongPass123!"
        result = validate_password_strength(strong_password)
        assert result["is_valid"] == True
        assert result["score"] >= 4
        
        # كلمة مرور ضعيفة
        weak_password = "123"
        result = validate_password_strength(weak_password)
        assert result["is_valid"] == False
        assert len(result["errors"]) > 0

class TestAuthManager:
    """اختبارات مدير المصادقة"""
    
    def test_user_registration(self):
        """اختبار تسجيل المستخدمين"""
        # تسجيل مستخدم جديد
        result = auth_manager.register_user(
            username="test_user",
            email="<EMAIL>",
            password="TestPass123!",
            full_name="مستخدم تجريبي",
            phone="0501234567",
            role="member"
        )
        
        assert result["success"] == True
        assert "user" in result
        assert result["user"]["username"] == "test_user"
    
    def test_user_login(self):
        """اختبار تسجيل الدخول"""
        # محاولة تسجيل دخول بالمستخدم الافتراضي
        result = auth_manager.login("admin", "admin123")
        
        assert result["success"] == True
        assert "access_token" in result
        assert "user" in result
        assert result["user"]["username"] == "admin"
    
    def test_invalid_login(self):
        """اختبار تسجيل دخول خاطئ"""
        result = auth_manager.login("invalid_user", "wrong_password")
        
        assert result["success"] == False
        assert "access_token" not in result

class TestFundManager:
    """اختبارات مدير الصناديق"""
    
    def setup_method(self):
        """إعداد قبل كل اختبار"""
        # تسجيل دخول المستخدم الافتراضي
        login_result = auth_manager.login("admin", "admin123")
        assert login_result["success"] == True
        self.user_id = login_result["user"]["id"]
    
    def test_create_fund(self):
        """اختبار إنشاء صندوق"""
        result = fund_manager.create_fund(
            name="صندوق تجريبي",
            description="صندوق للاختبار",
            fund_type="family",
            owner_id=self.user_id
        )
        
        assert result["success"] == True
        assert "fund" in result
        assert result["fund"]["name"] == "صندوق تجريبي"
    
    def test_get_user_funds(self):
        """اختبار الحصول على صناديق المستخدم"""
        funds = fund_manager.get_user_funds(self.user_id)
        
        assert isinstance(funds, list)
        # يجب أن يكون هناك على الأقل الصندوق الذي أنشأناه في الاختبار السابق
        assert len(funds) >= 0

class TestMemberManager:
    """اختبارات مدير الأعضاء"""
    
    def setup_method(self):
        """إعداد قبل كل اختبار"""
        # تسجيل دخول وإنشاء صندوق للاختبار
        login_result = auth_manager.login("admin", "admin123")
        self.user_id = login_result["user"]["id"]
        
        fund_result = fund_manager.create_fund(
            name="صندوق الأعضاء",
            description="صندوق لاختبار الأعضاء",
            fund_type="family",
            owner_id=self.user_id
        )
        self.fund_id = fund_result["fund"]["id"]
    
    def test_add_member(self):
        """اختبار إضافة عضو"""
        result = member_manager.add_member(
            fund_id=self.fund_id,
            name="عضو تجريبي",
            phone="0501234567",
            email="<EMAIL>",
            initial_balance=1000.0
        )
        
        assert result["success"] == True
        assert "member" in result
        assert result["member"]["name"] == "عضو تجريبي"
        assert result["member"]["balance"] == 1000.0
    
    def test_get_fund_members(self):
        """اختبار الحصول على أعضاء الصندوق"""
        members = member_manager.get_fund_members(self.fund_id)
        
        assert isinstance(members, list)

class TestFamilyManager:
    """اختبارات مدير العائلات"""
    
    def setup_method(self):
        """إعداد قبل كل اختبار"""
        login_result = auth_manager.login("admin", "admin123")
        self.user_id = login_result["user"]["id"]
        
        fund_result = fund_manager.create_fund(
            name="صندوق العائلات",
            description="صندوق لاختبار العائلات",
            fund_type="family",
            owner_id=self.user_id
        )
        self.fund_id = fund_result["fund"]["id"]
    
    def test_create_family(self):
        """اختبار إنشاء عائلة"""
        result = family_manager.create_family(
            fund_id=self.fund_id,
            name="عائلة تجريبية",
            description="عائلة للاختبار"
        )
        
        assert result["success"] == True
        assert "family" in result
        assert result["family"]["name"] == "عائلة تجريبية"
    
    def test_get_fund_families(self):
        """اختبار الحصول على عائلات الصندوق"""
        families = family_manager.get_fund_families(self.fund_id)
        
        assert isinstance(families, list)

class TestTransactionManager:
    """اختبارات مدير العمليات المالية"""
    
    def setup_method(self):
        """إعداد قبل كل اختبار"""
        login_result = auth_manager.login("admin", "admin123")
        self.user_id = login_result["user"]["id"]
        
        # إنشاء صندوق
        fund_result = fund_manager.create_fund(
            name="صندوق العمليات",
            description="صندوق لاختبار العمليات المالية",
            fund_type="family",
            owner_id=self.user_id
        )
        self.fund_id = fund_result["fund"]["id"]
        
        # إضافة عضو
        member_result = member_manager.add_member(
            fund_id=self.fund_id,
            name="عضو للعمليات",
            initial_balance=500.0
        )
        self.member_id = member_result["member"]["id"]
    
    def test_add_transaction(self):
        """اختبار إضافة عملية مالية"""
        result = transaction_manager.add_transaction(
            fund_id=self.fund_id,
            amount=100.0,
            transaction_type="income",
            description="إيراد تجريبي",
            created_by=self.user_id,
            member_id=self.member_id
        )
        
        assert result["success"] == True
        assert result["transactions_count"] == 1
    
    def test_get_fund_transactions(self):
        """اختبار الحصول على عمليات الصندوق"""
        transactions = transaction_manager.get_fund_transactions(self.fund_id)
        
        assert isinstance(transactions, list)

class TestDatabase:
    """اختبارات قاعدة البيانات"""
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        with db_manager.get_session() as session:
            assert session is not None
    
    def test_database_info(self):
        """اختبار الحصول على معلومات قاعدة البيانات"""
        info = db_manager.get_database_info()
        
        assert isinstance(info, dict)
        assert "database_path" in info
        assert "tables" in info
    
    def test_backup_creation(self):
        """اختبار إنشاء نسخة احتياطية"""
        backup_path = db_manager.backup_database()
        
        assert backup_path is not None
        assert os.path.exists(backup_path)

# دوال مساعدة للاختبارات
def test_project_structure():
    """اختبار هيكل المشروع"""
    # التأكد من وجود الملفات الأساسية
    assert os.path.exists("main.py")
    assert os.path.exists("web_app.py")
    assert os.path.exists("requirements.txt")
    assert os.path.exists("README.md")
    
    # التأكد من وجود المجلدات الأساسية
    assert os.path.exists("src")
    assert os.path.exists("web")
    assert os.path.exists("scripts")
    assert os.path.exists("data")

def test_requirements_file():
    """اختبار ملف المتطلبات"""
    with open("requirements.txt", "r", encoding="utf-8") as f:
        content = f.read()
    
    # التأكد من وجود المكتبات الأساسية
    assert "kivy" in content
    assert "kivymd" in content
    assert "fastapi" in content
    assert "sqlalchemy" in content

if __name__ == "__main__":
    # تشغيل الاختبارات
    pytest.main([__file__, "-v"])
