{% extends "base.html" %}

{% block title %}الخدمات الإضافية - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-tools me-2"></i>
        الخدمات الإضافية
    </h1>
</div>

<!-- خدمة التقويم -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    خدمة التقويم
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- التحويل بين التقاويم -->
                    <div class="col-md-6 mb-4">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">تحويل التقويم</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">نوع التحويل</label>
                                    <select class="form-control" id="conversion_type" onchange="toggleConversionFields()">
                                        <option value="gregorian_to_hijri">ميلادي إلى هجري</option>
                                        <option value="hijri_to_gregorian">هجري إلى ميلادي</option>
                                    </select>
                                </div>
                                
                                <!-- حقول التاريخ الميلادي -->
                                <div id="gregorian_fields">
                                    <div class="mb-3">
                                        <label class="form-label">التاريخ الميلادي</label>
                                        <input type="date" class="form-control" id="gregorian_date">
                                    </div>
                                </div>
                                
                                <!-- حقول التاريخ الهجري -->
                                <div id="hijri_fields" style="display: none;">
                                    <div class="row">
                                        <div class="col-4">
                                            <label class="form-label">السنة</label>
                                            <input type="number" class="form-control" id="hijri_year" placeholder="1445">
                                        </div>
                                        <div class="col-4">
                                            <label class="form-label">الشهر</label>
                                            <select class="form-control" id="hijri_month">
                                                <option value="1">محرم</option>
                                                <option value="2">صفر</option>
                                                <option value="3">ربيع الأول</option>
                                                <option value="4">ربيع الثاني</option>
                                                <option value="5">جمادى الأولى</option>
                                                <option value="6">جمادى الثانية</option>
                                                <option value="7">رجب</option>
                                                <option value="8">شعبان</option>
                                                <option value="9">رمضان</option>
                                                <option value="10">شوال</option>
                                                <option value="11">ذو القعدة</option>
                                                <option value="12">ذو الحجة</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <label class="form-label">اليوم</label>
                                            <input type="number" class="form-control" id="hijri_day" min="1" max="30" placeholder="15">
                                        </div>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary mt-3" onclick="convertCalendar()">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    تحويل
                                </button>
                                
                                <div id="conversion_result" class="mt-3" style="display: none;">
                                    <!-- نتيجة التحويل -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- حساب الفرق بين التواريخ -->
                    <div class="col-md-6 mb-4">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">حساب الفرق بين التواريخ</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="start_date">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="end_date">
                                </div>
                                
                                <button class="btn btn-success" onclick="calculateDateDifference()">
                                    <i class="fas fa-calculator me-2"></i>
                                    حساب الفرق
                                </button>
                                
                                <div id="difference_result" class="mt-3" style="display: none;">
                                    <!-- نتيجة الحساب -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات التاريخ الحالي -->
                <div class="row">
                    <div class="col-12">
                        <div class="card bg-info text-white">
                            <div class="card-header">
                                <h6 class="mb-0">التاريخ الحالي</h6>
                            </div>
                            <div class="card-body" id="current_date_info">
                                <button class="btn btn-light btn-sm" onclick="loadCurrentDate()">
                                    <i class="fas fa-sync me-2"></i>
                                    تحديث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- خدمة الملاحظات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    الملاحظات والتذكيرات
                </h5>
                <button class="btn btn-primary btn-sm" onclick="showAddNoteModal()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة ملاحظة
                </button>
            </div>
            <div class="card-body">
                <div id="notes_container">
                    <!-- الملاحظات ستظهر هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة ملاحظة -->
<div class="modal fade" id="addNoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ملاحظة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="noteForm">
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="note_title" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المحتوى</label>
                        <textarea class="form-control" id="note_content" rows="4" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">اللون</label>
                            <input type="color" class="form-control" id="note_color" value="#ffffff">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تذكير في</label>
                            <input type="datetime-local" class="form-control" id="note_reminder">
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="note_pinned">
                            <label class="form-check-label" for="note_pinned">
                                تثبيت الملاحظة
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveNote()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تبديل حقول التحويل
function toggleConversionFields() {
    const type = document.getElementById('conversion_type').value;
    const gregorianFields = document.getElementById('gregorian_fields');
    const hijriFields = document.getElementById('hijri_fields');
    
    if (type === 'gregorian_to_hijri') {
        gregorianFields.style.display = 'block';
        hijriFields.style.display = 'none';
    } else {
        gregorianFields.style.display = 'none';
        hijriFields.style.display = 'block';
    }
}

// تحويل التقويم
function convertCalendar() {
    const type = document.getElementById('conversion_type').value;
    let data = { type: type };
    
    if (type === 'gregorian_to_hijri') {
        data.date = document.getElementById('gregorian_date').value;
        if (!data.date) {
            alert('يرجى إدخال التاريخ الميلادي');
            return;
        }
    } else {
        data.year = parseInt(document.getElementById('hijri_year').value);
        data.month = parseInt(document.getElementById('hijri_month').value);
        data.day = parseInt(document.getElementById('hijri_day').value);
        
        if (!data.year || !data.day) {
            alert('يرجى إدخال التاريخ الهجري كاملاً');
            return;
        }
    }
    
    fetch('/api/services/calendar/convert', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayConversionResult(result);
        } else {
            alert('خطأ في التحويل: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في التحويل');
    });
}

// عرض نتيجة التحويل
function displayConversionResult(result) {
    const container = document.getElementById('conversion_result');
    
    let html = `
        <div class="alert alert-success">
            <h6>نتيجة التحويل:</h6>
            <p><strong>${result.formatted}</strong></p>
            <small class="text-muted">${result.note || ''}</small>
        </div>
    `;
    
    container.innerHTML = html;
    container.style.display = 'block';
}

// حساب الفرق بين التواريخ
function calculateDateDifference() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (!startDate || !endDate) {
        alert('يرجى إدخال التاريخين');
        return;
    }
    
    fetch('/api/services/calendar/difference', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayDifferenceResult(result);
        } else {
            alert('خطأ في الحساب: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الحساب');
    });
}

// عرض نتيجة حساب الفرق
function displayDifferenceResult(result) {
    const container = document.getElementById('difference_result');
    
    let html = `
        <div class="alert alert-info">
            <h6>الفرق بين التاريخين:</h6>
            <p><strong>${result.formatted}</strong></p>
            <ul class="mb-0">
                <li>إجمالي الأيام: ${result.total_days}</li>
                <li>إجمالي الأسابيع: ${result.total_weeks}</li>
            </ul>
        </div>
    `;
    
    container.innerHTML = html;
    container.style.display = 'block';
}

// تحميل معلومات التاريخ الحالي
function loadCurrentDate() {
    fetch('/api/services/calendar/current')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayCurrentDate(result);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// عرض معلومات التاريخ الحالي
function displayCurrentDate(result) {
    const container = document.getElementById('current_date_info');
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>التاريخ الميلادي</h6>
                <p>${result.gregorian.formatted}</p>
                <small>يوم ${result.gregorian.weekday}</small>
            </div>
            <div class="col-md-6">
                <h6>التاريخ الهجري (تقريبي)</h6>
                <p>${result.hijri.formatted}</p>
                <small>الوقت: ${result.current_time}</small>
            </div>
        </div>
        <button class="btn btn-light btn-sm mt-2" onclick="loadCurrentDate()">
            <i class="fas fa-sync me-2"></i>
            تحديث
        </button>
    `;
    
    container.innerHTML = html;
}

// إظهار مودال إضافة ملاحظة
function showAddNoteModal() {
    document.getElementById('noteForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('addNoteModal'));
    modal.show();
}

// حفظ الملاحظة
function saveNote() {
    const title = document.getElementById('note_title').value;
    const content = document.getElementById('note_content').value;
    const color = document.getElementById('note_color').value;
    const reminder = document.getElementById('note_reminder').value;
    const pinned = document.getElementById('note_pinned').checked;
    
    if (!title || !content) {
        alert('يرجى إدخال العنوان والمحتوى');
        return;
    }
    
    const data = {
        title: title,
        content: content,
        color: color,
        is_pinned: pinned
    };
    
    if (reminder) {
        data.reminder_at = reminder;
    }
    
    fetch('/api/notes', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('addNoteModal')).hide();
            loadNotes();
        } else {
            alert('خطأ في حفظ الملاحظة: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ الملاحظة');
    });
}

// تحميل الملاحظات
function loadNotes() {
    fetch('/api/notes')
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayNotes(result.data);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// عرض الملاحظات
function displayNotes(notes) {
    const container = document.getElementById('notes_container');
    
    if (notes.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-sticky-note fa-3x mb-3"></i>
                <p>لا توجد ملاحظات حتى الآن</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="row">';
    
    notes.forEach(note => {
        const isPinned = note.is_pinned ? '<i class="fas fa-thumbtack text-warning"></i>' : '';
        const reminderInfo = note.reminder_at ? 
            `<small class="text-muted"><i class="fas fa-bell me-1"></i>تذكير: ${new Date(note.reminder_at).toLocaleString('ar-SA')}</small>` : '';
        
        html += `
            <div class="col-md-4 mb-3">
                <div class="card" style="background-color: ${note.color}; border-left: 4px solid #007bff;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title">${note.title} ${isPinned}</h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="deleteNote(${note.id})">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <p class="card-text">${note.content}</p>
                        ${reminderInfo}
                        <small class="text-muted d-block mt-2">
                            ${new Date(note.created_at).toLocaleDateString('ar-SA')}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// حذف ملاحظة
function deleteNote(noteId) {
    if (!confirm('هل أنت متأكد من حذف هذه الملاحظة؟')) {
        return;
    }
    
    fetch(`/api/notes/${noteId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            loadNotes();
        } else {
            alert('خطأ في حذف الملاحظة: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حذف الملاحظة');
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('gregorian_date').value = today;
    document.getElementById('start_date').value = today;
    document.getElementById('end_date').value = today;
    
    // تحميل معلومات التاريخ الحالي
    loadCurrentDate();
    
    // تحميل الملاحظات
    loadNotes();
});
</script>
{% endblock %}
