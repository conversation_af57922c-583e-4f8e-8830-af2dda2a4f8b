{% extends "base.html" %}

{% block title %}خطأ {{ error_code }} - صندوق التوفير{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg">
                <div class="card-body text-center p-5">
                    {% if error_code == 404 %}
                    <div class="mb-4">
                        <i class="fas fa-search fa-5x text-warning"></i>
                    </div>
                    <h1 class="display-4 text-warning">404</h1>
                    <h3 class="mb-3">الصفحة غير موجودة</h3>
                    <p class="text-muted mb-4">عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.</p>
                    {% elif error_code == 500 %}
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle fa-5x text-danger"></i>
                    </div>
                    <h1 class="display-4 text-danger">500</h1>
                    <h3 class="mb-3">خطأ في الخادم</h3>
                    <p class="text-muted mb-4">حدث خطأ داخلي في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>
                    {% else %}
                    <div class="mb-4">
                        <i class="fas fa-exclamation-circle fa-5x text-info"></i>
                    </div>
                    <h1 class="display-4 text-info">{{ error_code }}</h1>
                    <h3 class="mb-3">حدث خطأ</h3>
                    <p class="text-muted mb-4">{{ error_message }}</p>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-block">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للخلف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.fa-5x {
    opacity: 0.8;
}
</style>
{% endblock %}
