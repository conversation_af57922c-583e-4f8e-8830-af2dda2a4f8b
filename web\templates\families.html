{% extends "base.html" %}

{% block title %}إدارة العائلات - صندوق التوفير{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-home me-2"></i>
        إدارة العائلات
    </h1>
    <button class="btn btn-primary" onclick="showCreateFamilyModal()">
        <i class="fas fa-plus me-2"></i>
        إنشاء عائلة جديدة
    </button>
</div>

<!-- معلومات الصندوق النشط -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="alert alert-info" id="active_fund_info">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <div>
                    <strong>الصندوق النشط:</strong>
                    <span id="current_fund_name">لم يتم اختيار صندوق</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <label for="search_families" class="form-label">البحث في العائلات</label>
        <input type="text" class="form-control" id="search_families" placeholder="ابحث باسم العائلة" onkeyup="filterFamilies()">
    </div>
</div>

<!-- قائمة العائلات -->
<div class="row" id="families_container">
    <div class="col-12">
        <div class="text-center text-muted py-5">
            <i class="fas fa-home fa-4x mb-3"></i>
            <h4>يرجى اختيار صندوق لعرض العائلات</h4>
        </div>
    </div>
</div>

<!-- مودال إنشاء عائلة جديدة -->
<div class="modal fade" id="createFamilyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء عائلة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createFamilyForm">
                    <div class="mb-3">
                        <label class="form-label">اسم العائلة</label>
                        <input type="text" class="form-control" id="family_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="family_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رب العائلة</label>
                        <select class="form-control" id="family_head">
                            <option value="">اختر رب العائلة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createFamily()">إنشاء العائلة</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل العائلة -->
<div class="modal fade" id="familyDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العائلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="family_details_content">
                <!-- تفاصيل العائلة ستظهر هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال إدارة أعضاء العائلة -->
<div class="modal fade" id="manageMembersModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة أعضاء العائلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الأعضاء الحاليون</h6>
                        <div id="current_members" class="border rounded p-3" style="min-height: 200px;">
                            <!-- الأعضاء الحاليون -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>الأعضاء المتاحون</h6>
                        <div id="available_members" class="border rounded p-3" style="min-height: 200px;">
                            <!-- الأعضاء المتاحون -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="saveFamilyMembers()">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allFamilies = [];
let currentFundId = null;
let currentFamilyId = null;

// تحميل العائلات للصندوق النشط
function loadFamilies() {
    const activeFund = getActiveFund();
    currentFundId = activeFund.id;

    // تحديث معلومات الصندوق النشط
    updateActiveFundInfo();

    if (!currentFundId) {
        document.getElementById('families_container').innerHTML = `
            <div class="col-12">
                <div class="text-center text-muted py-5">
                    <i class="fas fa-home fa-4x mb-3"></i>
                    <h4>يرجى اختيار صندوق نشط من القائمة العلوية</h4>
                </div>
            </div>
        `;
        return;
    }

    fetch(`/api/funds/${currentFundId}/families`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            allFamilies = result.data;
            displayFamilies(allFamilies);
            loadAvailableMembers(fundId);
        } else {
            showAlert('خطأ في تحميل العائلات: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل العائلات', 'danger');
    });
}

// عرض العائلات
function displayFamilies(families) {
    const container = document.getElementById('families_container');
    
    if (families.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center text-muted py-5">
                    <i class="fas fa-home fa-4x mb-3"></i>
                    <h4>لا توجد عائلات في هذا الصندوق</h4>
                    <p>ابدأ بإنشاء عائلة جديدة</p>
                    <button class="btn btn-primary" onclick="showCreateFamilyModal()">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء عائلة جديدة
                    </button>
                </div>
            </div>
        `;
        return;
    }
    
    let html = '';
    families.forEach(family => {
        html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-home me-2"></i>
                            <strong>${family.name}</strong>
                        </div>
                        <span class="badge bg-info">${family.total_members} عضو</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">${family.description || 'لا يوجد وصف'}</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <h6 class="text-muted">إجمالي الرصيد</h6>
                                <h5 class="text-success">${family.total_balance.toFixed(2)} ريال</h5>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted">عدد الأعضاء</h6>
                                <h5 class="text-info">${family.total_members}</h5>
                            </div>
                        </div>
                        ${family.head_name ? `
                        <div class="mt-3">
                            <small class="text-muted">رب العائلة: <strong>${family.head_name}</strong></small>
                        </div>
                        ` : ''}
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-primary btn-sm" onclick="viewFamilyDetails(${family.id})">
                                <i class="fas fa-eye me-1"></i>
                                تفاصيل
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="manageFamilyMembers(${family.id})">
                                <i class="fas fa-users me-1"></i>
                                الأعضاء
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteFamily(${family.id})">
                                <i class="fas fa-trash me-1"></i>
                                حذف
                            </button>
                        </div>
                        <small class="text-muted d-block mt-2">
                            تم الإنشاء: ${new Date(family.created_at).toLocaleDateString('ar-SA')}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// تصفية العائلات
function filterFamilies() {
    const searchTerm = document.getElementById('search_families').value.toLowerCase();
    
    if (!searchTerm) {
        displayFamilies(allFamilies);
        return;
    }
    
    const filteredFamilies = allFamilies.filter(family => 
        family.name.toLowerCase().includes(searchTerm) ||
        (family.description && family.description.toLowerCase().includes(searchTerm))
    );
    
    displayFamilies(filteredFamilies);
}

// إظهار مودال إنشاء عائلة
function showCreateFamilyModal() {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }
    
    document.getElementById('createFamilyForm').reset();
    loadAvailableMembers(currentFundId);
    const modal = new bootstrap.Modal(document.getElementById('createFamilyModal'));
    modal.show();
}

// تحميل الأعضاء المتاحين
function loadAvailableMembers(fundId) {
    fetch(`/api/funds/${fundId}/members`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const headSelect = document.getElementById('family_head');
            headSelect.innerHTML = '<option value="">اختر رب العائلة</option>';
            
            result.data.forEach(member => {
                headSelect.innerHTML += `<option value="${member.id}">${member.name}</option>`;
            });
        }
    })
    .catch(error => {
        console.error('Error loading members:', error);
    });
}

// إنشاء عائلة جديدة
function createFamily() {
    if (!currentFundId) {
        showAlert('يرجى اختيار صندوق أولاً', 'warning');
        return;
    }
    
    const name = document.getElementById('family_name').value;
    const description = document.getElementById('family_description').value;
    const headId = document.getElementById('family_head').value || null;
    
    if (!name) {
        showAlert('يرجى إدخال اسم العائلة', 'warning');
        return;
    }
    
    const data = {
        name: name,
        description: description,
        head_id: headId
    };
    
    fetch(`/api/funds/${currentFundId}/families`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('createFamilyModal')).hide();
            showAlert('تم إنشاء العائلة بنجاح', 'success');
            loadFamilies();
        } else {
            showAlert('خطأ في إنشاء العائلة: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في إنشاء العائلة', 'danger');
    });
}

// عرض تفاصيل العائلة
function viewFamilyDetails(familyId) {
    fetch(`/api/families/${familyId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            displayFamilyDetails(result.data);
            const modal = new bootstrap.Modal(document.getElementById('familyDetailsModal'));
            modal.show();
        } else {
            showAlert('خطأ في تحميل تفاصيل العائلة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في تحميل التفاصيل', 'danger');
    });
}

// عرض تفاصيل العائلة في المودال
function displayFamilyDetails(family) {
    const content = document.getElementById('family_details_content');
    
    let membersHtml = '';
    if (family.members && family.members.length > 0) {
        family.members.forEach(member => {
            const balanceClass = member.balance >= 0 ? 'text-success' : 'text-danger';
            membersHtml += `
                <tr>
                    <td>${member.name}</td>
                    <td>${member.phone || '-'}</td>
                    <td class="${balanceClass}">${member.balance.toFixed(2)} ريال</td>
                </tr>
            `;
        });
    } else {
        membersHtml = '<tr><td colspan="3" class="text-center text-muted">لا يوجد أعضاء</td></tr>';
    }
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات العائلة</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>الاسم:</strong></td>
                        <td>${family.name}</td>
                    </tr>
                    <tr>
                        <td><strong>الوصف:</strong></td>
                        <td>${family.description || '-'}</td>
                    </tr>
                    <tr>
                        <td><strong>رب العائلة:</strong></td>
                        <td>${family.head_name || '-'}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإنشاء:</strong></td>
                        <td>${new Date(family.created_at).toLocaleDateString('ar-SA')}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>الإحصائيات</h6>
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6>إجمالي الرصيد</h6>
                                <h4>${family.total_balance.toFixed(2)} ريال</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h6>عدد الأعضاء</h6>
                                <h4>${family.total_members}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>أعضاء العائلة</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الجوال</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${membersHtml}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

// إدارة أعضاء العائلة
function manageFamilyMembers(familyId) {
    currentFamilyId = familyId;
    // هذه الوظيفة ستكون مبسطة في الوقت الحالي
    showAlert('ميزة إدارة الأعضاء قيد التطوير', 'info');
}

// حذف العائلة
function deleteFamily(familyId) {
    if (!confirm('هل أنت متأكد من حذف هذه العائلة؟\nسيتم إزالة جميع الأعضاء من العائلة.')) {
        return;
    }
    
    fetch(`/api/families/${familyId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('تم حذف العائلة بنجاح', 'success');
            loadFamilies();
        } else {
            showAlert('خطأ في حذف العائلة: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في حذف العائلة', 'danger');
    });
}

// عرض رسالة تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحديث معلومات الصندوق النشط
function updateActiveFundInfo() {
    const activeFund = getActiveFund();
    const infoElement = document.getElementById('active_fund_info');
    const nameElement = document.getElementById('current_fund_name');

    if (activeFund.id && activeFund.name) {
        nameElement.textContent = activeFund.name;
        infoElement.className = 'alert alert-success';
    } else {
        nameElement.textContent = 'لم يتم اختيار صندوق';
        infoElement.className = 'alert alert-warning';
    }
}

// إعادة تحميل بيانات الصفحة عند تغيير الصندوق النشط
function reloadPageData() {
    loadFamilies();
}

// الاستماع لتغيير الصندوق النشط
window.addEventListener('activeFundChanged', function(event) {
    reloadPageData();
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل العائلات للصندوق النشط
    loadFamilies();
});
</script>
{% endblock %}
