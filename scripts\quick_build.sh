#!/bin/bash
# -*- coding: utf-8 -*-
# سكربت البناء السريع لتطبيق صندوق التوفير

set -e  # إيقاف التنفيذ عند حدوث خطأ

# الألوان للمخرجات
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دوال المساعدة
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️ $1${NC}"
}

# التحقق من وجود Python
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_error "Python غير مثبت على النظام"
        exit 1
    fi
    
    print_success "تم العثور على Python: $PYTHON_CMD"
}

# تثبيت المتطلبات
install_requirements() {
    print_header "تثبيت المتطلبات"
    
    if [ -f "requirements.txt" ]; then
        print_info "تثبيت المتطلبات من requirements.txt..."
        $PYTHON_CMD -m pip install -r requirements.txt
        print_success "تم تثبيت المتطلبات الأساسية"
    else
        print_warning "ملف requirements.txt غير موجود"
    fi
    
    # تثبيت أدوات البناء
    print_info "تثبيت أدوات البناء..."
    $PYTHON_CMD -m pip install pyinstaller buildozer cx-freeze auto-py-to-exe
    print_success "تم تثبيت أدوات البناء"
}

# تشغيل التطبيق المحلي
run_desktop() {
    print_header "تشغيل التطبيق المحلي"
    print_info "بدء تشغيل التطبيق..."
    $PYTHON_CMD main.py
}

# تشغيل خادم الويب
run_web() {
    print_header "تشغيل خادم الويب"
    print_info "بدء تشغيل خادم الويب على http://localhost:8000"
    $PYTHON_CMD web_app.py
}

# بناء تطبيق Windows
build_windows() {
    print_header "بناء تطبيق Windows"
    
    if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" && "$OSTYPE" != "cygwin" ]]; then
        print_warning "بناء Windows متاح فقط على نظام Windows"
        return 1
    fi
    
    print_info "بدء بناء تطبيق Windows..."
    $PYTHON_CMD scripts/build_all.py --platform windows
    
    if [ $? -eq 0 ]; then
        print_success "تم بناء تطبيق Windows بنجاح"
        print_info "الملفات متوفرة في مجلد dist/"
    else
        print_error "فشل في بناء تطبيق Windows"
        return 1
    fi
}

# بناء تطبيق Android
build_android() {
    print_header "بناء تطبيق Android"
    
    # التحقق من وجود buildozer
    if ! command -v buildozer &> /dev/null; then
        print_info "تثبيت buildozer..."
        $PYTHON_CMD -m pip install buildozer
    fi
    
    print_info "بدء بناء تطبيق Android..."
    print_warning "هذا قد يستغرق وقتاً طويلاً في المرة الأولى..."
    
    $PYTHON_CMD scripts/build_all.py --platform android
    
    if [ $? -eq 0 ]; then
        print_success "تم بناء تطبيق Android بنجاح"
        print_info "ملف APK متوفر في مجلد dist/"
    else
        print_error "فشل في بناء تطبيق Android"
        return 1
    fi
}

# إعداد تطبيق الويب
build_web() {
    print_header "إعداد تطبيق الويب"
    
    print_info "إعداد ملفات تطبيق الويب..."
    $PYTHON_CMD scripts/build_all.py --platform web
    
    if [ $? -eq 0 ]; then
        print_success "تم إعداد تطبيق الويب بنجاح"
        print_info "الملفات متوفرة في مجلد dist/"
    else
        print_error "فشل في إعداد تطبيق الويب"
        return 1
    fi
}

# بناء جميع الإصدارات
build_all() {
    print_header "بناء جميع الإصدارات"
    
    print_info "بدء بناء جميع إصدارات التطبيق..."
    $PYTHON_CMD scripts/build_all.py --platform all
    
    if [ $? -eq 0 ]; then
        print_success "تم بناء جميع الإصدارات بنجاح"
        print_info "جميع الملفات متوفرة في مجلد dist/"
        
        # عرض الملفات المُنشأة
        if [ -d "dist" ]; then
            print_info "الملفات المُنشأة:"
            ls -la dist/
        fi
    else
        print_error "فشل في بناء بعض الإصدارات"
        return 1
    fi
}

# تنظيف ملفات البناء
clean() {
    print_header "تنظيف ملفات البناء"
    
    print_info "حذف ملفات البناء المؤقتة..."
    
    # حذف مجلدات البناء
    rm -rf build/
    rm -rf dist/
    rm -rf *.egg-info/
    rm -rf __pycache__/
    rm -rf .buildozer/
    rm -rf bin/
    
    # حذف ملفات مؤقتة
    find . -name "*.pyc" -delete
    find . -name "*.pyo" -delete
    find . -name "__pycache__" -type d -exec rm -rf {} +
    
    print_success "تم تنظيف ملفات البناء"
}

# إنشاء نسخة احتياطية
backup() {
    print_header "إنشاء نسخة احتياطية"
    
    BACKUP_NAME="tawfeer_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    print_info "إنشاء نسخة احتياطية: $BACKUP_NAME"
    
    tar -czf "$BACKUP_NAME" \
        --exclude='build' \
        --exclude='dist' \
        --exclude='.buildozer' \
        --exclude='bin' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='*.pyo' \
        --exclude='.git' \
        .
    
    print_success "تم إنشاء النسخة الاحتياطية: $BACKUP_NAME"
}

# تشغيل الاختبارات
test() {
    print_header "تشغيل الاختبارات"
    
    if [ -d "tests" ]; then
        print_info "تشغيل الاختبارات..."
        $PYTHON_CMD -m pytest tests/ -v
        
        if [ $? -eq 0 ]; then
            print_success "جميع الاختبارات نجحت"
        else
            print_error "بعض الاختبارات فشلت"
            return 1
        fi
    else
        print_warning "مجلد الاختبارات غير موجود"
    fi
}

# عرض المساعدة
show_help() {
    echo -e "${PURPLE}سكربت البناء السريع لتطبيق صندوق التوفير${NC}"
    echo ""
    echo "الاستخدام: $0 [الأمر]"
    echo ""
    echo "الأوامر المتاحة:"
    echo "  install     - تثبيت المتطلبات"
    echo "  run         - تشغيل التطبيق المحلي"
    echo "  web         - تشغيل خادم الويب"
    echo "  build-win   - بناء تطبيق Windows"
    echo "  build-android - بناء تطبيق Android"
    echo "  build-web   - إعداد تطبيق الويب"
    echo "  build-all   - بناء جميع الإصدارات"
    echo "  clean       - تنظيف ملفات البناء"
    echo "  backup      - إنشاء نسخة احتياطية"
    echo "  test        - تشغيل الاختبارات"
    echo "  help        - عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0 install"
    echo "  $0 run"
    echo "  $0 build-all"
}

# الدالة الرئيسية
main() {
    # التحقق من وجود Python
    check_python
    
    # التحقق من المعامل
    case "${1:-help}" in
        "install")
            install_requirements
            ;;
        "run")
            run_desktop
            ;;
        "web")
            run_web
            ;;
        "build-win")
            build_windows
            ;;
        "build-android")
            build_android
            ;;
        "build-web")
            build_web
            ;;
        "build-all")
            build_all
            ;;
        "clean")
            clean
            ;;
        "backup")
            backup
            ;;
        "test")
            test
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# تشغيل الدالة الرئيسية
main "$@"
