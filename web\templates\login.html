{% extends "base.html" %}

{% block title %}تسجيل الدخول - صندوق التوفير{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow-lg">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-piggy-bank fa-3x text-primary mb-3"></i>
                    <h2 class="card-title">صندوق التوفير</h2>
                    <p class="text-muted">تسجيل الدخول إلى حسابك</p>
                </div>

                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ error }}
                </div>
                {% endif %}

                <form method="post" action="/login">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2"></i>
                            اسم المستخدم أو البريد الإلكتروني
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>
                            كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </div>
                </form>

                <hr class="my-4">

                <div class="text-center">
                    <p class="text-muted mb-2">ليس لديك حساب؟</p>
                    <button class="btn btn-outline-primary" onclick="showRegisterModal()">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء حساب جديد
                    </button>
                </div>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        للدخول كمدير استخدم: admin / admin123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal التسجيل -->
<div class="modal fade" id="registerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء حساب جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="registerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="reg_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="reg_username" name="username" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reg_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="reg_email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reg_full_name" class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="reg_full_name" name="full_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reg_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="reg_phone" name="phone">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="reg_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="reg_password" name="password" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reg_confirm_password" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="reg_confirm_password" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reg_role" class="form-label">نوع الحساب</label>
                        <select class="form-control" id="reg_role" name="role">
                            <option value="member">عضو</option>
                            <option value="supervisor">مشرف</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="registerUser()">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء الحساب
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showRegisterModal() {
    const modal = new bootstrap.Modal(document.getElementById('registerModal'));
    modal.show();
}

function registerUser() {
    const form = document.getElementById('registerForm');
    const formData = new FormData(form);
    
    // التحقق من تطابق كلمات المرور
    const password = document.getElementById('reg_password').value;
    const confirmPassword = document.getElementById('reg_confirm_password').value;
    
    if (password !== confirmPassword) {
        alert('كلمات المرور غير متطابقة');
        return;
    }
    
    // إرسال البيانات
    fetch('/api/register', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.');
            const modal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
            modal.hide();
            form.reset();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إنشاء الحساب');
    });
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // التركيز على حقل اسم المستخدم
    document.getElementById('username').focus();
    
    // إضافة تأثيرات بصرية
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script>

<style>
.focused {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.fa-piggy-bank {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
</style>
{% endblock %}
