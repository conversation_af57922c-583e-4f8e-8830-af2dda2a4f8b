#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لتطبيق صندوق التوفير
يقوم بتثبيت المتطلبات وتشغيل التطبيق تلقائياً
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
from pathlib import Path

def print_banner():
    """طباعة شعار التطبيق"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    🏦 صندوق التوفير 🏦                      ║
    ║                                                              ║
    ║              تطبيق شامل لإدارة الصناديق والأعضاء              ║
    ║                                                              ║
    ║                     التشغيل السريع                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def install_requirements():
    """تثبيت المتطلبات الأساسية"""
    print("📦 تثبيت المتطلبات الأساسية...")
    
    # المتطلبات الأساسية للتشغيل السريع
    basic_requirements = [
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "jinja2>=3.1.0",
        "python-multipart>=0.0.6",
        "sqlalchemy>=2.0.0",
        "bcrypt>=4.0.0",
        "cryptography>=41.0.0",
        "pyjwt>=2.8.0",
        "python-dateutil>=2.8.0"
    ]
    
    for requirement in basic_requirements:
        try:
            print(f"تثبيت {requirement}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", requirement
            ], check=True, capture_output=True)
            print(f"✅ تم تثبيت {requirement}")
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {requirement}")
    
    print("✅ تم تثبيت المتطلبات الأساسية")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        # إنشاء المجلدات المطلوبة
        os.makedirs("data", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        os.makedirs("config", exist_ok=True)
        
        # تشغيل إعداد قاعدة البيانات
        from src.database.database import db_manager
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def start_web_server():
    """تشغيل خادم الويب"""
    print("🌐 تشغيل خادم الويب...")
    print("📍 الرابط: http://localhost:8000")
    
    # فتح المتصفح بعد 3 ثوان
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8000")
            print("🌐 تم فتح المتصفح")
        except Exception as e:
            print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")
            print("يرجى فتح الرابط يدوياً: http://localhost:8000")
    
    # تشغيل فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # تشغيل خادم الويب
        import uvicorn
        from web_app import app
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except ImportError:
        print("❌ uvicorn غير متوفر، محاولة تثبيته...")
        subprocess.run([sys.executable, "-m", "pip", "install", "uvicorn[standard]"])
        
        import uvicorn
        from web_app import app
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🚀 بدء التشغيل السريع...")
    
    # التحقق من وجود Python
    print(f"🐍 Python {sys.version.split()[0]}")
    
    # تثبيت المتطلبات
    install_requirements()
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        return
    
    print("\n" + "="*60)
    print("✅ تم إعداد التطبيق بنجاح!")
    print("🌐 سيتم تشغيل تطبيق الويب الآن...")
    print("📍 الرابط: http://localhost:8000")
    print("👤 للدخول كمدير: admin / admin123")
    print("="*60)
    
    # انتظار قليل قبل التشغيل
    time.sleep(2)
    
    # تشغيل خادم الويب
    start_web_server()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 شكراً لاستخدام صندوق التوفير!")
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")
        print("يرجى المحاولة مرة أخرى أو استخدام python run.py للمزيد من الخيارات")
