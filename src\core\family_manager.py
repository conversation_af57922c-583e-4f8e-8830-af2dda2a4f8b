#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير العائلات - إدارة جميع عمليات العائلات
يحتوي على جميع الوظائف المتعلقة بإدارة العائلات في الصناديق
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from datetime import datetime
import logging

from ..database.models import Family, Fund, Member, Transaction, TransactionType
from ..database.database import db_manager

logger = logging.getLogger(__name__)

class FamilyManager:
    """مدير العائلات"""
    
    def __init__(self):
        """تهيئة مدير العائلات"""
        pass
    
    def create_family(
        self,
        fund_id: int,
        name: str,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        إنشاء عائلة جديدة
        
        Args:
            fund_id: معرف الصندوق
            name: اسم العائلة
            description: وصف العائلة
            
        Returns:
            معلومات العائلة المُنشأة
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من وجود الصندوق
                fund = session.query(Fund).filter_by(id=fund_id, is_active=True).first()
                if not fund:
                    return {"success": False, "message": "الصندوق غير موجود"}
                
                # التحقق من عدم تكرار اسم العائلة في نفس الصندوق
                existing_family = session.query(Family).filter(
                    and_(Family.name == name, Family.fund_id == fund_id)
                ).first()
                
                if existing_family:
                    return {"success": False, "message": "يوجد عائلة بنفس الاسم في الصندوق"}
                
                # إنشاء العائلة الجديدة
                new_family = Family(
                    name=name,
                    description=description,
                    fund_id=fund_id
                )
                
                session.add(new_family)
                session.commit()
                session.refresh(new_family)
                
                logger.info(f"تم إنشاء عائلة جديدة: {name} في الصندوق {fund.name}")
                
                return {
                    "success": True,
                    "message": "تم إنشاء العائلة بنجاح",
                    "family": {
                        "id": new_family.id,
                        "name": new_family.name,
                        "description": new_family.description,
                        "total_members": new_family.total_members,
                        "total_balance": new_family.total_balance,
                        "created_at": new_family.created_at.isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء العائلة: {e}")
            return {"success": False, "message": f"خطأ في إنشاء العائلة: {str(e)}"}
    
    def get_fund_families(self, fund_id: int) -> List[Dict[str, Any]]:
        """
        الحصول على جميع عائلات الصندوق
        
        Args:
            fund_id: معرف الصندوق
            
        Returns:
            قائمة بعائلات الصندوق
        """
        try:
            with db_manager.get_session() as session:
                families = session.query(Family).filter_by(fund_id=fund_id).all()
                
                families_list = []
                for family in families:
                    # حساب عدد الأعضاء الفعلي
                    actual_members_count = session.query(Member).filter(
                        and_(Member.family_id == family.id, Member.is_active == True)
                    ).count()
                    
                    # حساب الرصيد الفعلي
                    actual_balance = session.query(func.sum(Member.balance)).filter(
                        and_(Member.family_id == family.id, Member.is_active == True)
                    ).scalar() or 0
                    
                    # تحديث البيانات إذا كانت مختلفة
                    if family.total_members != actual_members_count or family.total_balance != actual_balance:
                        family.total_members = actual_members_count
                        family.total_balance = float(actual_balance)
                        session.commit()
                    
                    families_list.append({
                        "id": family.id,
                        "name": family.name,
                        "description": family.description,
                        "total_members": family.total_members,
                        "total_balance": family.total_balance,
                        "created_at": family.created_at.isoformat(),
                        "updated_at": family.updated_at.isoformat()
                    })
                
                return families_list
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على عائلات الصندوق: {e}")
            return []
    
    def get_family_details(self, family_id: int, fund_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل عائلة معينة
        
        Args:
            family_id: معرف العائلة
            fund_id: معرف الصندوق
            
        Returns:
            تفاصيل العائلة أو None
        """
        try:
            with db_manager.get_session() as session:
                family = session.query(Family).filter(
                    and_(Family.id == family_id, Family.fund_id == fund_id)
                ).first()
                
                if not family:
                    return None
                
                # الحصول على أعضاء العائلة
                members = session.query(Member).filter(
                    and_(Member.family_id == family_id, Member.is_active == True)
                ).all()
                
                members_list = []
                total_income = 0
                total_expense = 0
                total_transactions = 0
                
                for member in members:
                    # حساب إحصائيات العضو
                    member_transactions = session.query(Transaction).filter_by(member_id=member.id).all()
                    
                    member_income = sum(t.amount for t in member_transactions if t.transaction_type == TransactionType.INCOME)
                    member_expense = sum(t.amount for t in member_transactions if t.transaction_type == TransactionType.EXPENSE)
                    
                    total_income += member_income
                    total_expense += member_expense
                    total_transactions += len(member_transactions)
                    
                    members_list.append({
                        "id": member.id,
                        "name": member.name,
                        "phone": member.phone,
                        "balance": member.balance,
                        "transactions_count": len(member_transactions),
                        "total_income": float(member_income),
                        "total_expense": float(member_expense)
                    })
                
                return {
                    "id": family.id,
                    "name": family.name,
                    "description": family.description,
                    "total_members": len(members_list),
                    "total_balance": family.total_balance,
                    "statistics": {
                        "total_income": float(total_income),
                        "total_expense": float(total_expense),
                        "total_transactions": total_transactions,
                        "average_balance": family.total_balance / len(members_list) if members_list else 0
                    },
                    "members": members_list,
                    "created_at": family.created_at.isoformat(),
                    "updated_at": family.updated_at.isoformat()
                }
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل العائلة: {e}")
            return None
    
    def update_family(
        self,
        family_id: int,
        fund_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        تحديث معلومات العائلة
        
        Args:
            family_id: معرف العائلة
            fund_id: معرف الصندوق
            name: الاسم الجديد
            description: الوصف الجديد
            
        Returns:
            نتيجة التحديث
        """
        try:
            with db_manager.get_session() as session:
                family = session.query(Family).filter(
                    and_(Family.id == family_id, Family.fund_id == fund_id)
                ).first()
                
                if not family:
                    return {"success": False, "message": "العائلة غير موجودة"}
                
                # تحديث الحقول المطلوبة
                if name:
                    # التحقق من عدم تكرار الاسم
                    existing_family = session.query(Family).filter(
                        and_(
                            Family.name == name,
                            Family.fund_id == fund_id,
                            Family.id != family_id
                        )
                    ).first()
                    
                    if existing_family:
                        return {"success": False, "message": "يوجد عائلة أخرى بنفس الاسم"}
                    
                    family.name = name
                
                if description is not None:
                    family.description = description
                
                family.updated_at = datetime.utcnow()
                session.commit()
                
                logger.info(f"تم تحديث العائلة: {family.name}")
                
                return {"success": True, "message": "تم تحديث العائلة بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في تحديث العائلة: {e}")
            return {"success": False, "message": f"خطأ في تحديث العائلة: {str(e)}"}
    
    def delete_family(self, family_id: int, fund_id: int) -> Dict[str, Any]:
        """
        حذف عائلة
        
        Args:
            family_id: معرف العائلة
            fund_id: معرف الصندوق
            
        Returns:
            نتيجة الحذف
        """
        try:
            with db_manager.get_session() as session:
                family = session.query(Family).filter(
                    and_(Family.id == family_id, Family.fund_id == fund_id)
                ).first()
                
                if not family:
                    return {"success": False, "message": "العائلة غير موجودة"}
                
                # التحقق من وجود أعضاء في العائلة
                members_count = session.query(Member).filter(
                    and_(Member.family_id == family_id, Member.is_active == True)
                ).count()
                
                if members_count > 0:
                    return {
                        "success": False, 
                        "message": f"لا يمكن حذف العائلة لوجود {members_count} أعضاء بها. يرجى نقل الأعضاء أولاً."
                    }
                
                # حذف العائلة
                session.delete(family)
                session.commit()
                
                logger.info(f"تم حذف العائلة: {family.name}")
                
                return {"success": True, "message": "تم حذف العائلة بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في حذف العائلة: {e}")
            return {"success": False, "message": f"خطأ في حذف العائلة: {str(e)}"}
    
    def add_member_to_family(self, member_id: int, family_id: int, fund_id: int) -> Dict[str, Any]:
        """
        إضافة عضو إلى عائلة
        
        Args:
            member_id: معرف العضو
            family_id: معرف العائلة
            fund_id: معرف الصندوق
            
        Returns:
            نتيجة الإضافة
        """
        try:
            with db_manager.get_session() as session:
                # التحقق من وجود العضو
                member = session.query(Member).filter(
                    and_(Member.id == member_id, Member.fund_id == fund_id, Member.is_active == True)
                ).first()
                
                if not member:
                    return {"success": False, "message": "العضو غير موجود"}
                
                # التحقق من وجود العائلة
                family = session.query(Family).filter(
                    and_(Family.id == family_id, Family.fund_id == fund_id)
                ).first()
                
                if not family:
                    return {"success": False, "message": "العائلة غير موجودة"}
                
                # إزالة العضو من العائلة القديمة إذا كان ينتمي لعائلة
                if member.family_id:
                    old_family = session.query(Family).filter_by(id=member.family_id).first()
                    if old_family:
                        old_family.total_members -= 1
                        old_family.total_balance -= member.balance
                
                # إضافة العضو للعائلة الجديدة
                member.family_id = family_id
                family.total_members += 1
                family.total_balance += member.balance
                
                session.commit()
                
                logger.info(f"تم إضافة العضو {member.name} للعائلة {family.name}")
                
                return {"success": True, "message": "تم إضافة العضو للعائلة بنجاح"}
                
        except Exception as e:
            logger.error(f"خطأ في إضافة العضو للعائلة: {e}")
            return {"success": False, "message": f"خطأ في إضافة العضو للعائلة: {str(e)}"}

# إنشاء مثيل مدير العائلات
family_manager = FamilyManager()
