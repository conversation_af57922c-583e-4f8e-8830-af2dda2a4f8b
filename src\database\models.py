#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لتطبيق صندوق التوفير
يحتوي على جميع الجداول والعلاقات المطلوبة
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, 
    Text, ForeignKey, Enum, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

Base = declarative_base()

class UserRole(enum.Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"      # مدير
    SUPERVISOR = "supervisor"  # مشرف
    MEMBER = "member"    # عضو

class TransactionType(enum.Enum):
    """أنواع العمليات المالية"""
    INCOME = "income"        # إيراد
    EXPENSE = "expense"      # مصروف
    WITHDRAWAL = "withdrawal"  # سحب
    LOAN = "loan"           # سلفة
    DEPOSIT = "deposit"     # إيداع

class FundType(enum.Enum):
    """أنواع الصناديق"""
    FAMILY = "family"       # عائلي
    INDIVIDUAL = "individual"  # فردي
    PERSONAL = "personal"   # شخصي

# جدول المستخدمين
class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    phone = Column(String(20))
    role = Column(Enum(UserRole), default=UserRole.MEMBER)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    funds = relationship("Fund", back_populates="owner")
    created_transactions = relationship("Transaction", back_populates="created_by_user")

# جدول الصناديق
class Fund(Base):
    __tablename__ = 'funds'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    fund_type = Column(Enum(FundType), nullable=False)
    password_hash = Column(String(255))  # حماية الصندوق بكلمة مرور
    total_balance = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    owner_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    owner = relationship("User", back_populates="funds")
    members = relationship("Member", back_populates="fund")
    families = relationship("Family", back_populates="fund")
    transactions = relationship("Transaction", back_populates="fund")
    notes = relationship("Note", back_populates="fund")

# جدول العائلات
class Family(Base):
    __tablename__ = 'families'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    fund_id = Column(Integer, ForeignKey('funds.id'), nullable=False)
    total_members = Column(Integer, default=0)
    total_balance = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    fund = relationship("Fund", back_populates="families")
    members = relationship("Member", back_populates="family")
    
    __table_args__ = (UniqueConstraint('name', 'fund_id', name='unique_family_per_fund'),)

# جدول الأعضاء
class Member(Base):
    __tablename__ = 'members'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20))
    email = Column(String(100))
    national_id = Column(String(20))
    address = Column(Text)
    balance = Column(Float, default=0.0)
    fund_id = Column(Integer, ForeignKey('funds.id'), nullable=False)
    family_id = Column(Integer, ForeignKey('families.id'), nullable=True)
    is_active = Column(Boolean, default=True)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    fund = relationship("Fund", back_populates="members")
    family = relationship("Family", back_populates="members")
    transactions = relationship("Transaction", back_populates="member")

# جدول العمليات المالية
class Transaction(Base):
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    amount = Column(Float, nullable=False)
    transaction_type = Column(Enum(TransactionType), nullable=False)
    description = Column(Text)
    fund_id = Column(Integer, ForeignKey('funds.id'), nullable=False)
    member_id = Column(Integer, ForeignKey('members.id'), nullable=True)
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    transaction_date = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    fund = relationship("Fund", back_populates="transactions")
    member = relationship("Member", back_populates="transactions")
    created_by_user = relationship("User", back_populates="created_transactions")

# جدول الملاحظات والتنبيهات
class Note(Base):
    __tablename__ = 'notes'

    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    color = Column(String(7), default="#FFFFFF")  # كود اللون hex

    # المالك
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    fund_id = Column(Integer, ForeignKey('funds.id'), nullable=True)
    member_id = Column(Integer, ForeignKey('members.id'), nullable=True)

    # التوقيت
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reminder_at = Column(DateTime, nullable=True)  # تذكير مجدول

    # الحالة
    is_pinned = Column(Boolean, default=False)     # مثبتة
    is_archived = Column(Boolean, default=False)   # مؤرشفة
    is_active = Column(Boolean, default=True)

    # العلاقات
    fund = relationship("Fund", back_populates="notes")

# جدول النسخ الاحتياطية
class Backup(Base):
    __tablename__ = 'backups'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # بالبايت
    backup_type = Column(String(50))  # auto, manual
    created_at = Column(DateTime, default=datetime.utcnow)
    
# جدول إعدادات التطبيق
class Setting(Base):
    __tablename__ = 'settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text)
    description = Column(Text)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
