#!/bin/bash
# -*- coding: utf-8 -*-
# ملف تشغيل سريع لتطبيق صندوق التوفير على Linux/macOS

# الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة الشعار
print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║                    🏦 صندوق التوفير 🏦                      ║"
    echo "║                                                              ║"
    echo "║              تطبيق شامل لإدارة الصناديق والأعضاء              ║"
    echo "║                                                              ║"
    echo "║                        الإصدار 1.0.0                        ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# دالة عرض القائمة
show_menu() {
    echo -e "${CYAN}══════════════════════════════════════════════════════════════${NC}"
    echo -e "${YELLOW}🎯 اختر العملية المطلوبة:${NC}"
    echo -e "${CYAN}══════════════════════════════════════════════════════════════${NC}"
    echo -e "${GREEN}1.${NC} 🖥️  تشغيل تطبيق سطح المكتب"
    echo -e "${GREEN}2.${NC} 🌐 تشغيل تطبيق الويب"
    echo -e "${GREEN}3.${NC} 📦 تثبيت المتطلبات"
    echo -e "${GREEN}4.${NC} 🧪 تشغيل الاختبارات"
    echo -e "${GREEN}5.${NC} 🔨 بناء المشروع"
    echo -e "${GREEN}6.${NC} 🧹 تنظيف المشروع"
    echo -e "${GREEN}7.${NC} ℹ️  عرض معلومات المشروع"
    echo -e "${GREEN}8.${NC} ⚙️  إعداد المشروع"
    echo -e "${RED}0.${NC} 🚪 خروج"
    echo -e "${CYAN}══════════════════════════════════════════════════════════════${NC}"
}

# دالة التحقق من وجود Python
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python غير مثبت على النظام${NC}"
        exit 1
    fi
}

# دالة تشغيل تطبيق سطح المكتب
run_desktop() {
    echo -e "${BLUE}🖥️ تشغيل تطبيق سطح المكتب...${NC}"
    $PYTHON_CMD run.py --desktop
}

# دالة تشغيل تطبيق الويب
run_web() {
    echo -e "${BLUE}🌐 تشغيل تطبيق الويب...${NC}"
    echo -e "${CYAN}📍 سيتم فتح المتصفح على: http://localhost:8000${NC}"
    $PYTHON_CMD run.py --web
}

# دالة تثبيت المتطلبات
install_requirements() {
    echo -e "${BLUE}📦 تثبيت المتطلبات...${NC}"
    $PYTHON_CMD run.py --install
}

# دالة تشغيل الاختبارات
run_tests() {
    echo -e "${BLUE}🧪 تشغيل الاختبارات...${NC}"
    $PYTHON_CMD run.py --test
}

# دالة بناء المشروع
build_project() {
    echo -e "${BLUE}🔨 بناء المشروع...${NC}"
    $PYTHON_CMD run.py --build
}

# دالة تنظيف المشروع
clean_project() {
    echo -e "${BLUE}🧹 تنظيف المشروع...${NC}"
    $PYTHON_CMD run.py --clean
}

# دالة عرض معلومات المشروع
show_info() {
    echo -e "${BLUE}ℹ️ عرض معلومات المشروع...${NC}"
    $PYTHON_CMD run.py --info
}

# دالة إعداد المشروع
setup_project() {
    echo -e "${BLUE}⚙️ إعداد المشروع...${NC}"
    $PYTHON_CMD run.py --setup
}

# الدالة الرئيسية
main() {
    # التحقق من وجود Python
    check_python
    
    # إذا تم تمرير معامل، تنفيذه مباشرة
    if [ $# -gt 0 ]; then
        case $1 in
            "desktop"|"1")
                run_desktop
                ;;
            "web"|"2")
                run_web
                ;;
            "install"|"3")
                install_requirements
                ;;
            "test"|"4")
                run_tests
                ;;
            "build"|"5")
                build_project
                ;;
            "clean"|"6")
                clean_project
                ;;
            "info"|"7")
                show_info
                ;;
            "setup"|"8")
                setup_project
                ;;
            "help"|"-h"|"--help")
                print_banner
                echo -e "${YELLOW}الاستخدام:${NC}"
                echo "  $0 [الأمر]"
                echo ""
                echo -e "${YELLOW}الأوامر المتاحة:${NC}"
                echo "  desktop, 1    - تشغيل تطبيق سطح المكتب"
                echo "  web, 2        - تشغيل تطبيق الويب"
                echo "  install, 3    - تثبيت المتطلبات"
                echo "  test, 4       - تشغيل الاختبارات"
                echo "  build, 5      - بناء المشروع"
                echo "  clean, 6      - تنظيف المشروع"
                echo "  info, 7       - عرض معلومات المشروع"
                echo "  setup, 8      - إعداد المشروع"
                echo "  help          - عرض هذه المساعدة"
                ;;
            *)
                echo -e "${RED}❌ أمر غير معروف: $1${NC}"
                echo "استخدم '$0 help' لعرض المساعدة"
                exit 1
                ;;
        esac
        return
    fi
    
    # القائمة التفاعلية
    print_banner
    
    while true; do
        echo ""
        show_menu
        echo ""
        read -p "👆 اختر رقم العملية: " choice
        
        case $choice in
            1)
                run_desktop
                ;;
            2)
                run_web
                ;;
            3)
                install_requirements
                ;;
            4)
                run_tests
                ;;
            5)
                build_project
                ;;
            6)
                clean_project
                ;;
            7)
                show_info
                ;;
            8)
                setup_project
                ;;
            0)
                echo -e "${GREEN}👋 شكراً لاستخدام صندوق التوفير!${NC}"
                break
                ;;
            *)
                echo -e "${RED}❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى${NC}"
                ;;
        esac
        
        echo ""
        read -p "اضغط Enter للمتابعة..."
    done
}

# تشغيل الدالة الرئيسية
main "$@"
